package qidian.it.springboot.service;

import com.baomidou.mybatisplus.extension.service.IService;
import qidian.it.springboot.entity.Orders;

import java.util.List;
import java.util.Map;

/**
 * 订单服务接口
 */
public interface OrdersService extends IService<Orders> {
    
    /**
     * 创建订单（从购物车）
     */
    Orders createOrderFromCart(Long userId, List<Long> cartItemIds);
    
    /**
     * 创建订单（直接购买）
     */
    Orders createOrderDirect(Long userId, Long productId, Integer quantity);
    
    /**
     * 获取用户订单列表
     */
    List<Map<String, Object>> getUserOrders(Long userId);
    
    /**
     * 获取订单详情
     */
    Map<String, Object> getOrderDetail(Long orderId);
    
    /**
     * 根据订单号查询订单
     */
    Orders getOrderByOrderNo(String orderNo);
    
    /**
     * 更新订单支付状态
     */
    boolean updatePayStatus(String orderNo, Integer status);
    
    /**
     * 取消订单
     */
    boolean cancelOrder(Long orderId, Long userId);
    
    /**
     * 确认收货
     */
    boolean confirmOrder(Long orderId, Long userId);
    
    /**
     * 获取用户订单统计
     */
    Map<String, Object> getOrderStatistics(Long userId);
    
    /**
     * 生成订单号
     */
    String generateOrderNo();

    /**
     * 获取用户待支付订单列表
     */
    List<Map<String, Object>> getUserPendingOrders(Long userId);

    // ========================================
    // 管理员订单管理方法
    // ========================================

    /**
     * 获取所有订单列表（管理员）
     */
    List<Map<String, Object>> getAllOrdersForAdmin();

    /**
     * 更新订单状态（管理员）
     */
    boolean updateOrderStatusByAdmin(Long orderId, Integer status, String remark);

    /**
     * 获取订单统计信息（管理员）
     */
    Map<String, Object> getAdminOrderStatistics();

    /**
     * 搜索订单列表（管理员）
     */
    List<Map<String, Object>> searchOrdersForAdmin(String keyword, Integer status);

    /**
     * 退款后更新订单状态
     */
    boolean updateOrderStatusAfterRefund(String orderNo);
}
