package qidian.it.springboot.controller;

import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.request.AlipayTradePagePayRequest;
import com.alipay.api.request.AlipayTradeQueryRequest;
import com.alipay.api.request.AlipayTradeRefundRequest;
import com.alipay.api.response.AlipayTradeQueryResponse;
import com.alipay.api.response.AlipayTradeRefundResponse;
import com.alipay.easysdk.factory.Factory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import qidian.it.springboot.config.AliPayConfig;
import qidian.it.springboot.entity.AliPay;
import qidian.it.springboot.entity.Orders;
import qidian.it.springboot.service.OrdersService;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 支付宝支付控制器
 */
@RestController
@RequestMapping("/api/alipay")
@Transactional(rollbackFor = Exception.class)
public class AliPayController {

    @Autowired
    private AliPayConfig aliPayConfig;

    @Autowired
    private OrdersService ordersService;

    private static final String GATEWAY_URL = "https://openapi-sandbox.dl.alipaydev.com/gateway.do";
    private static final String FORMAT = "JSON";
    private static final String CHARSET = "utf-8";
    private static final String SIGN_TYPE = "RSA2";

    /**
     * 支付接口
     * 参数：subject=商品名称&traceNo=订单号&totalAmount=金额
     */
    @GetMapping("/pay")
    public void pay(AliPay aliPay, HttpServletResponse httpResponse) throws Exception {
        AlipayClient alipayClient = new DefaultAlipayClient(
                GATEWAY_URL, 
                aliPayConfig.getAppId(),
                aliPayConfig.getAppPrivateKey(), 
                FORMAT, 
                CHARSET, 
                aliPayConfig.getAlipayPublicKey(), 
                SIGN_TYPE
        );
        
        AlipayTradePagePayRequest request = new AlipayTradePagePayRequest();
        request.setNotifyUrl(aliPayConfig.getNotifyUrl());
        request.setReturnUrl("http://localhost:8082/api/alipay/return"); // 同步回调地址
        request.setBizContent("{\"out_trade_no\":\"" + aliPay.getTraceNo() + "\","
                + "\"total_amount\":\"" + aliPay.getTotalAmount() + "\","
                + "\"subject\":\"" + aliPay.getSubject() + "\","
                + "\"product_code\":\"FAST_INSTANT_TRADE_PAY\"}");
        
        String form = "";
        try {
            // 调用SDK生成表单
            form = alipayClient.pageExecute(request).getBody();
        } catch (AlipayApiException e) {
            e.printStackTrace();
        }
        
        httpResponse.setContentType("text/html;charset=" + CHARSET);
        // 直接将完整的表单html输出到页面
        httpResponse.getWriter().write(form);
        httpResponse.getWriter().flush();
        httpResponse.getWriter().close();
    }

    /**
     * 支付宝异步回调接口
     * 注意这里必须是POST接口
     */
    @PostMapping("/notify")
    public String payNotify(HttpServletRequest request) throws Exception {
        System.out.println("=========支付宝异步回调========");

        Map<String, String> params = new HashMap<>();
        Map<String, String[]> requestParams = request.getParameterMap();
        for (String name : requestParams.keySet()) {
            params.put(name, request.getParameter(name));
            System.out.println(name + " = " + request.getParameter(name));
        }

        String tradeStatus = params.get("trade_status");
        String tradeNo = params.get("out_trade_no");

        System.out.println("交易状态: " + tradeStatus);
        System.out.println("商户订单号: " + tradeNo);

        try {
            // 支付宝验签
            if (Factory.Payment.Common().verifyNotify(params)) {
                System.out.println("异步回调验签成功");

                // 如果是支付成功，更新订单状态
                if ("TRADE_SUCCESS".equals(tradeStatus) || "TRADE_FINISHED".equals(tradeStatus)) {
                    System.out.println("交易名称: " + params.get("subject"));
                    System.out.println("支付宝交易凭证号: " + params.get("trade_no"));
                    System.out.println("交易金额: " + params.get("total_amount"));
                    System.out.println("买家在支付宝唯一id: " + params.get("buyer_id"));
                    System.out.println("买家付款时间: " + params.get("gmt_payment"));
                    System.out.println("买家付款金额: " + params.get("buyer_pay_amount"));

                    // 更新订单为已支付状态
                    boolean success = ordersService.updatePayStatus(tradeNo, 1);
                    if (success) {
                        System.out.println("异步回调：订单支付状态更新成功");
                    } else {
                        System.out.println("异步回调：订单支付状态更新失败");
                    }
                } else {
                    System.out.println("交易状态不是成功状态: " + tradeStatus);
                }
            } else {
                System.out.println("异步回调验签失败");
            }
        } catch (Exception e) {
            System.out.println("异步回调处理异常: " + e.getMessage());
            e.printStackTrace();
        }

        return "success";
    }

    /**
     * 支付成功页面跳转（同步回调）
     */
    @GetMapping("/return")
    public void payReturn(HttpServletRequest request, HttpServletResponse response) throws Exception {
        System.out.println("=========支付宝同步回调========");

        Map<String, String> params = new HashMap<>();
        Map<String, String[]> requestParams = request.getParameterMap();
        for (String name : requestParams.keySet()) {
            params.put(name, request.getParameter(name));
            System.out.println(name + " = " + request.getParameter(name));
        }

        String orderNo = params.get("out_trade_no");
        String tradeStatus = params.get("trade_status");

        try {
            // 支付宝验签
            if (Factory.Payment.Common().verifyNotify(params)) {
                System.out.println("同步回调验签成功");

                // 如果是支付成功，更新订单状态
                if ("TRADE_SUCCESS".equals(tradeStatus) || "TRADE_FINISHED".equals(tradeStatus)) {
                    boolean success = ordersService.updatePayStatus(orderNo, 1);
                    if (success) {
                        System.out.println("同步回调：订单支付状态更新成功");
                    } else {
                        System.out.println("同步回调：订单支付状态更新失败");
                    }
                }

                // 跳转到前端支付成功页面
                String redirectUrl = "http://localhost:8083/orders?paySuccess=true&orderNo=" + orderNo;
                response.sendRedirect(redirectUrl);
            } else {
                System.out.println("同步回调验签失败");
                // 跳转到支付失败页面
                String redirectUrl = "http://localhost:8083/orders?paySuccess=false";
                response.sendRedirect(redirectUrl);
            }
        } catch (Exception e) {
            System.out.println("同步回调处理异常: " + e.getMessage());
            e.printStackTrace();
            // 跳转到支付失败页面
            String redirectUrl = "http://localhost:8083/orders?paySuccess=false";
            response.sendRedirect(redirectUrl);
        }
    }

    /**
     * 测试支付回调（用于本地测试）
     */
    @PostMapping("/test-callback")
    public Map<String, Object> testCallback(@RequestBody Map<String, Object> requestData) {
        Map<String, Object> result = new HashMap<>();
        try {
            String orderNo = requestData.get("orderNo").toString();
            String tradeStatus = requestData.get("tradeStatus").toString();

            System.out.println("=========测试支付回调========");
            System.out.println("订单号: " + orderNo);
            System.out.println("交易状态: " + tradeStatus);

            if ("TRADE_SUCCESS".equals(tradeStatus)) {
                // 更新订单为已支付状态
                boolean success = ordersService.updatePayStatus(orderNo, 1);
                if (success) {
                    System.out.println("测试回调：订单支付状态更新成功");
                    result.put("success", true);
                    result.put("message", "订单支付状态更新成功");
                } else {
                    System.out.println("测试回调：订单支付状态更新失败");
                    result.put("success", false);
                    result.put("message", "订单支付状态更新失败");
                }
            } else {
                result.put("success", false);
                result.put("message", "交易状态不是成功状态");
            }
        } catch (Exception e) {
            System.out.println("测试回调处理异常: " + e.getMessage());
            e.printStackTrace();
            result.put("success", false);
            result.put("message", "处理异常：" + e.getMessage());
        }
        return result;
    }

    /**
     * 手动检查支付状态并更新订单（用于支付完成后手动同步状态）
     */
    @PostMapping("/check-pay-status")
    public Map<String, Object> checkPayStatus(@RequestBody Map<String, Object> requestData) {
        Map<String, Object> result = new HashMap<>();
        try {
            String orderNo = requestData.get("orderNo").toString();

            System.out.println("=========手动检查支付状态========");
            System.out.println("订单号: " + orderNo);

            // 查询当前订单状态
            Orders order = ordersService.getOrderByOrderNo(orderNo);
            if (order == null) {
                result.put("success", false);
                result.put("message", "订单不存在");
                return result;
            }

            if (order.getStatus() == 1) {
                result.put("success", true);
                result.put("message", "订单已支付");
                result.put("alreadyPaid", true);
                return result;
            }

            // 这里可以调用支付宝查询接口来检查真实的支付状态
            // 为了简化，我们提供手动更新功能
            boolean success = ordersService.updatePayStatus(orderNo, 1);
            if (success) {
                System.out.println("手动更新：订单支付状态更新成功");
                result.put("success", true);
                result.put("message", "订单支付状态更新成功");
                result.put("updated", true);
            } else {
                System.out.println("手动更新：订单支付状态更新失败");
                result.put("success", false);
                result.put("message", "订单支付状态更新失败");
            }
        } catch (Exception e) {
            System.out.println("检查支付状态异常: " + e.getMessage());
            e.printStackTrace();
            result.put("success", false);
            result.put("message", "检查支付状态异常：" + e.getMessage());
        }
        return result;
    }

    /**
     * 查询订单支付状态（调用支付宝API查询真实状态）
     */
    @PostMapping("/query-pay-status")
    public Map<String, Object> queryPayStatus(@RequestBody Map<String, Object> requestData) {
        Map<String, Object> result = new HashMap<>();
        try {
            String orderNo = requestData.get("orderNo").toString();

            System.out.println("=========查询订单支付状态========");
            System.out.println("订单号: " + orderNo);

            // 查询当前订单状态
            Orders order = ordersService.getOrderByOrderNo(orderNo);
            if (order == null) {
                result.put("success", false);
                result.put("message", "订单不存在");
                return result;
            }

            if (order.getStatus() == 1) {
                result.put("success", true);
                result.put("message", "订单已支付");
                result.put("alreadyPaid", true);
                return result;
            }

            // 调用支付宝API查询订单支付状态
            try {
                AlipayTradeQueryRequest request = new AlipayTradeQueryRequest();
                request.setBizContent("{\"out_trade_no\":\"" + orderNo + "\"}");

                AlipayClient alipayClient = new DefaultAlipayClient(
                    GATEWAY_URL,
                    aliPayConfig.getAppId(),
                    aliPayConfig.getAppPrivateKey(),
                    FORMAT,
                    CHARSET,
                    aliPayConfig.getAlipayPublicKey(),
                    SIGN_TYPE
                );
                AlipayTradeQueryResponse response = alipayClient.execute(request);

                System.out.println("支付宝查询响应: " + response.getBody());

                if (response.isSuccess()) {
                    String tradeStatus = response.getTradeStatus();
                    System.out.println("交易状态: " + tradeStatus);

                    // 如果支付宝返回支付成功状态，更新本地订单状态
                    if ("TRADE_SUCCESS".equals(tradeStatus) || "TRADE_FINISHED".equals(tradeStatus)) {
                        boolean updateSuccess = ordersService.updatePayStatus(orderNo, 1);
                        if (updateSuccess) {
                            System.out.println("订单支付状态更新成功");
                            result.put("success", true);
                            result.put("message", "订单支付状态查询成功，订单已支付");
                            result.put("updated", true);
                            result.put("tradeStatus", tradeStatus);
                        } else {
                            result.put("success", false);
                            result.put("message", "支付宝显示已支付，但本地状态更新失败");
                        }
                    } else {
                        // 支付宝显示未支付
                        result.put("success", true);
                        result.put("message", "订单尚未支付");
                        result.put("updated", false);
                        result.put("tradeStatus", tradeStatus);
                    }
                } else {
                    System.out.println("支付宝查询失败: " + response.getSubMsg());
                    result.put("success", false);
                    result.put("message", "查询支付状态失败: " + response.getSubMsg());
                }
            } catch (Exception e) {
                System.out.println("调用支付宝API异常: " + e.getMessage());
                e.printStackTrace();
                result.put("success", false);
                result.put("message", "查询支付状态异常: " + e.getMessage());
            }

        } catch (Exception e) {
            System.out.println("查询支付状态异常: " + e.getMessage());
            e.printStackTrace();
            result.put("success", false);
            result.put("message", "查询支付状态异常：" + e.getMessage());
        }
        return result;
    }

    /**
     * 开发测试：强制确认订单支付状态（仅用于开发环境）
     */
    @PostMapping("/force-confirm-pay")
    public Map<String, Object> forceConfirmPay(@RequestBody Map<String, Object> requestData) {
        Map<String, Object> result = new HashMap<>();
        try {
            String orderNo = requestData.get("orderNo").toString();

            System.out.println("=========强制确认支付状态（开发测试）========");
            System.out.println("订单号: " + orderNo);

            // 查询当前订单状态
            Orders order = ordersService.getOrderByOrderNo(orderNo);
            if (order == null) {
                result.put("success", false);
                result.put("message", "订单不存在");
                return result;
            }

            if (order.getStatus() == 1) {
                result.put("success", true);
                result.put("message", "订单已支付");
                result.put("alreadyPaid", true);
                return result;
            }

            // 强制确认支付成功（仅用于开发测试）
            boolean success = ordersService.updatePayStatus(orderNo, 1);
            if (success) {
                System.out.println("强制确认：订单支付状态更新成功（开发测试）");
                result.put("success", true);
                result.put("message", "订单支付状态强制确认成功（开发测试）");
                result.put("updated", true);
                result.put("isForceConfirm", true);
            } else {
                System.out.println("强制确认：订单支付状态更新失败");
                result.put("success", false);
                result.put("message", "强制确认失败");
            }
        } catch (Exception e) {
            System.out.println("强制确认支付状态异常: " + e.getMessage());
            e.printStackTrace();
            result.put("success", false);
            result.put("message", "强制确认异常：" + e.getMessage());
        }
        return result;
    }

    /**
     * 支付宝退款接口
     */
    @PostMapping("/refund")
    public Map<String, Object> refund(@RequestBody Map<String, Object> requestData) {
        Map<String, Object> result = new HashMap<>();
        try {
            String orderNo = requestData.get("orderNo").toString();
            String refundAmount = requestData.get("refundAmount").toString();
            String refundReason = requestData.get("refundReason").toString();

            System.out.println("=========发起支付宝退款========");
            System.out.println("订单号: " + orderNo);
            System.out.println("退款金额: " + refundAmount);
            System.out.println("退款原因: " + refundReason);

            // 查询订单是否存在且已支付
            Orders order = ordersService.getOrderByOrderNo(orderNo);
            if (order == null) {
                result.put("success", false);
                result.put("message", "订单不存在");
                return result;
            }

            if (order.getStatus() != 1) {
                result.put("success", false);
                result.put("message", "只有已支付订单才能退款");
                return result;
            }

            // 调用支付宝退款API
            try {
                AlipayTradeRefundRequest request = new AlipayTradeRefundRequest();

                // 生成退款单号
                String refundNo = "RF" + System.currentTimeMillis();

                request.setBizContent("{" +
                    "\"out_trade_no\":\"" + orderNo + "\"," +
                    "\"refund_amount\":\"" + refundAmount + "\"," +
                    "\"out_request_no\":\"" + refundNo + "\"," +
                    "\"refund_reason\":\"" + refundReason + "\"" +
                    "}");

                AlipayClient alipayClient = new DefaultAlipayClient(
                    GATEWAY_URL,
                    aliPayConfig.getAppId(),
                    aliPayConfig.getAppPrivateKey(),
                    FORMAT,
                    CHARSET,
                    aliPayConfig.getAlipayPublicKey(),
                    SIGN_TYPE
                );

                AlipayTradeRefundResponse response = alipayClient.execute(request);

                System.out.println("支付宝退款响应: " + response.getBody());

                if (response.isSuccess()) {
                    // 退款成功，更新订单状态
                    boolean updateSuccess = ordersService.updateOrderStatusAfterRefund(orderNo);
                    if (updateSuccess) {
                        System.out.println("退款成功，订单状态已更新");
                        result.put("success", true);
                        result.put("message", "退款成功");
                        result.put("refundNo", refundNo);
                        result.put("refundAmount", response.getRefundFee());
                        result.put("buyerUserId", response.getBuyerUserId());
                    } else {
                        result.put("success", false);
                        result.put("message", "退款成功但订单状态更新失败");
                    }
                } else {
                    System.out.println("支付宝退款失败: " + response.getSubMsg());
                    result.put("success", false);
                    result.put("message", "退款失败: " + response.getSubMsg());
                }

            } catch (AlipayApiException e) {
                System.out.println("调用支付宝退款API异常: " + e.getMessage());
                e.printStackTrace();
                result.put("success", false);
                result.put("message", "退款异常: " + e.getMessage());
            }

        } catch (Exception e) {
            System.out.println("退款处理异常: " + e.getMessage());
            e.printStackTrace();
            result.put("success", false);
            result.put("message", "退款处理异常：" + e.getMessage());
        }
        return result;
    }
}
