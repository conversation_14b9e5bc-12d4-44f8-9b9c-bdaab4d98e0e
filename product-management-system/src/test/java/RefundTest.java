/**
 * 支付宝退款功能测试说明
 * 
 * 测试步骤：
 * 1. 启动后端服务 (端口8082)
 * 2. 启动前端服务 (端口8083)
 * 3. 登录用户账户
 * 4. 创建订单并完成支付
 * 5. 在订单列表中点击"申请退款"按钮
 * 6. 输入退款原因
 * 7. 确认退款申请
 * 8. 查看退款结果
 * 
 * 沙箱账户信息：
 * 商家账号：<EMAIL>
 * 商家密码：111111
 * 买家账号：<EMAIL>
 * 买家密码：111111
 * 支付密码：111111
 * 
 * 退款接口地址：
 * POST http://localhost:8082/api/alipay/refund
 * 
 * 请求参数：
 * {
 *   "orderNo": "订单号",
 *   "refundAmount": "退款金额",
 *   "refundReason": "退款原因"
 * }
 * 
 * 响应示例：
 * {
 *   "success": true,
 *   "message": "退款成功",
 *   "refundNo": "退款单号",
 *   "refundAmount": "实际退款金额"
 * }
 * 
 * 注意事项：
 * 1. 只有已支付状态(status=1)的订单才能申请退款
 * 2. 退款成功后订单状态会变为已退款(status=5)
 * 3. 退款成功后会自动恢复商品库存
 * 4. 支付宝沙箱环境的退款是即时到账的
 * 5. 生产环境退款通常需要1-3个工作日
 */
public class RefundTest {
    
    // 测试用例可以在这里添加
    
}
