<template>
  <div class="mall-container">
    <!-- 顶部导航栏 -->
    <header class="mall-header">
      <div class="header-content">
        <!-- Logo区域 -->
        <div class="logo-section">
          <div class="logo">
            <span class="logo-text">商城</span>
          </div>
        </div>
        
        <!-- 搜索区域 -->
        <div class="search-section">
          <div class="search-container">
            <el-input
              v-model="searchKeyword"
              placeholder="请输入商品名称搜索"
              class="search-input"
              @keyup.enter="handleSearch"
            >
              <template #append>
                <el-button 
                  type="danger" 
                  @click="handleSearch"
                  class="search-btn"
                >
                  搜索
                </el-button>
              </template>
            </el-input>
          </div>
          <!-- 热门搜索 -->
          <div class="hot-keywords">
            <span class="hot-label">热门:</span>
            <el-tag 
              v-for="keyword in hotKeywords" 
              :key="keyword"
              class="hot-tag"
              @click="searchByKeyword(keyword)"
            >
              {{ keyword }}
            </el-tag>
          </div>
        </div>
        
        <!-- 用户信息区域 -->
        <div class="user-section">
          <div class="user-info">
            <el-avatar :size="32" :src="userInfo.avatar">
              <el-icon><User /></el-icon>
            </el-avatar>
            <span class="username">{{ userInfo.username }}</span>
          </div>
          <el-dropdown @command="handleUserCommand">
            <span class="user-dropdown">
              <el-icon><ArrowDown /></el-icon>
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="profile">个人中心</el-dropdown-item>
                <el-dropdown-item command="orders">我的订单</el-dropdown-item>
                <el-dropdown-item command="cart">购物车</el-dropdown-item>
                <el-dropdown-item divided command="logout">退出登录</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="mall-main">
      <!-- 分类导航 -->
      <aside class="category-sidebar">
        <div class="category-title">商品分类</div>
        <ul class="category-list">
          <li 
            v-for="category in categories" 
            :key="category.id"
            class="category-item"
            :class="{ active: selectedCategory === category.id }"
            @click="selectCategory(category.id)"
          >
            <span class="category-name">{{ category.name }}</span>
            <el-icon class="category-arrow"><ArrowRight /></el-icon>
          </li>
        </ul>
      </aside>

      <!-- 商品展示区域 -->
      <section class="products-section">
        <!-- 搜索条件显示 -->
        <div v-if="searchKeyword || selectedCategory" class="search-conditions">
          <div class="condition-tags">
            <span class="condition-label">当前筛选：</span>
            <el-tag
              v-if="searchKeyword"
              type="primary"
              closable
              @close="searchKeyword = ''; handleSearch()"
              class="condition-tag"
            >
              关键词：{{ searchKeyword }}
            </el-tag>
            <el-tag
              v-if="selectedCategory"
              type="success"
              closable
              @close="selectCategory(selectedCategory)"
              class="condition-tag"
            >
              分类：{{ categories.find(c => c.id === selectedCategory)?.name }}
            </el-tag>
            <el-button
              type="info"
              size="small"
              @click="clearSearch"
              class="clear-all-btn"
            >
              清空所有筛选
            </el-button>
          </div>
        </div>

        <!-- 筛选工具栏 -->
        <div class="filter-toolbar">
          <div class="filter-left">
            <span class="result-count">共找到 {{ totalFilteredProducts }} 件商品</span>
          </div>
          <div class="filter-right">
            <el-select v-model="sortBy" placeholder="排序方式" @change="handleSort">
              <el-option label="综合排序" value="default" />
              <el-option label="价格从低到高" value="price_asc" />
              <el-option label="价格从高到低" value="price_desc" />
              <el-option label="最新上架" value="newest" />
            </el-select>
          </div>
        </div>

        <!-- 商品网格 -->
        <div class="products-grid">
          <!-- 骨架屏加载状态 -->
          <template v-if="loading">
            <div
              v-for="n in 8"
              :key="'skeleton-' + n"
              class="product-skeleton"
            >
              <div class="skeleton-image"></div>
              <div class="skeleton-content">
                <div class="skeleton-line title"></div>
                <div class="skeleton-line desc"></div>
                <div class="skeleton-line price"></div>
              </div>
            </div>
          </template>

          <!-- 商品列表 -->
          <template v-else-if="paginatedProducts.length > 0">
            <div
              v-for="product in paginatedProducts"
              :key="product.id"
              class="product-card"
              @click="viewProduct(product)"
            >
              <div class="product-image">
                <img
                  :src="getImageUrl(product.imageUrl) || '/images/default-product.svg'"
                  :alt="product.name"
                  @error="handleImageError"
                />
                <div class="product-overlay">
                  <el-button size="small" @click.stop="viewProduct(product)">
                    查看详情
                  </el-button>
                </div>
              </div>
              <div class="product-info">
                <h3 class="product-title">{{ product.name }}</h3>
                <p class="product-desc">{{ product.description }}</p>
                <div class="product-price">
                  <span class="current-price">¥{{ product.price }}</span>
                  <span class="original-price" v-if="product.originalPrice">¥{{ product.originalPrice }}</span>
                </div>
                <div class="product-actions">
                  <el-button
                    type="primary"
                    size="default"
                    @click.stop="addToCart(product)"
                    :loading="product.adding"
                    :icon="ShoppingCart"
                    class="add-to-cart-btn"
                  >
                    {{ product.adding ? '添加中...' : '加入购物车' }}
                  </el-button>
                </div>
              </div>
            </div>
          </template>

          <!-- 空状态 -->
          <div v-else class="empty-state">
            <el-icon><Box /></el-icon>
            <h3>暂无商品</h3>
            <p>当前分类下没有找到商品</p>
          </div>
        </div>

        <!-- 分页 -->
        <div class="pagination-container" v-if="totalFilteredProducts > 0">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[8, 12, 16, 24]"
            :total="totalFilteredProducts"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </section>
    </main>

    <!-- 京东风格底部页脚 -->
    <footer class="mall-footer">
      <!-- 服务承诺区域 -->
      <div class="service-promises">
        <div class="footer-content">
          <div class="promise-item">
            <div class="promise-icon">
              <el-icon><Van /></el-icon>
            </div>
            <div class="promise-text">
              <div class="promise-title">正品保障</div>
              <div class="promise-desc">正品保障，假一赔十</div>
            </div>
          </div>
          <div class="promise-item">
            <div class="promise-icon">
              <el-icon><Clock /></el-icon>
            </div>
            <div class="promise-text">
              <div class="promise-title">极速物流</div>
              <div class="promise-desc">急速物流，极致体验</div>
            </div>
          </div>
          <div class="promise-item">
            <div class="promise-icon">
              <el-icon><Shield /></el-icon>
            </div>
            <div class="promise-text">
              <div class="promise-title">无忧售后</div>
              <div class="promise-desc">7天无理由退换货</div>
            </div>
          </div>
          <div class="promise-item">
            <div class="promise-icon">
              <el-icon><Headset /></el-icon>
            </div>
            <div class="promise-text">
              <div class="promise-title">贴心服务</div>
              <div class="promise-desc">7×24小时客服</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 主要信息区域 -->
      <div class="footer-main">
        <div class="footer-content">
          <div class="footer-section">
            <h4 class="section-title">购物指南</h4>
            <ul class="section-links">
              <li><a href="#">购物流程</a></li>
              <li><a href="#">会员介绍</a></li>
              <li><a href="#">生活旅行</a></li>
              <li><a href="#">常见问题</a></li>
              <li><a href="#">大家电</a></li>
              <li><a href="#">联系客服</a></li>
            </ul>
          </div>
          <div class="footer-section">
            <h4 class="section-title">配送方式</h4>
            <ul class="section-links">
              <li><a href="#">上门自提</a></li>
              <li><a href="#">211限时达</a></li>
              <li><a href="#">配送服务查询</a></li>
              <li><a href="#">配送费收取标准</a></li>
              <li><a href="#">海外配送</a></li>
            </ul>
          </div>
          <div class="footer-section">
            <h4 class="section-title">支付方式</h4>
            <ul class="section-links">
              <li><a href="#">货到付款</a></li>
              <li><a href="#">在线支付</a></li>
              <li><a href="#">分期付款</a></li>
              <li><a href="#">邮局汇款</a></li>
              <li><a href="#">公司转账</a></li>
            </ul>
          </div>
          <div class="footer-section">
            <h4 class="section-title">售后服务</h4>
            <ul class="section-links">
              <li><a href="#">售后政策</a></li>
              <li><a href="#">价格保护</a></li>
              <li><a href="#">退款说明</a></li>
              <li><a href="#">返修/退换货</a></li>
              <li><a href="#">取消订单</a></li>
            </ul>
          </div>
          <div class="footer-section">
            <h4 class="section-title">特色服务</h4>
            <ul class="section-links">
              <li><a href="#">夺宝岛</a></li>
              <li><a href="#">DIY装机</a></li>
              <li><a href="#">延保服务</a></li>
              <li><a href="#">商城礼品卡</a></li>
              <li><a href="#">设备清洗</a></li>
            </ul>
          </div>
          <div class="footer-section admin-section">
            <h4 class="section-title">管理中心</h4>
            <ul class="section-links">
              <li><router-link to="/admin/login" class="admin-link">管理员登录</router-link></li>
              <li><a href="#">系统监控</a></li>
              <li><a href="#">数据统计</a></li>
              <li><a href="#">运营报告</a></li>
            </ul>
          </div>
        </div>
      </div>

      <!-- 版权信息区域 -->
      <div class="footer-bottom">
        <div class="footer-content">
          <div class="copyright-info">
            <div class="company-info">
              <p>Copyright © 2024 商城 版权所有</p>
              <p>
                <a href="#">关于我们</a> |
                <a href="#">联系我们</a> |
                <a href="#">人才招聘</a> |
                <a href="#">商家入驻</a> |
                <a href="#">广告服务</a> |
                <a href="#">手机商城</a> |
                <a href="#">友情链接</a> |
                <a href="#">销售联盟</a> |
                <a href="#">商城社区</a> |
                <a href="#">风险监测</a> |
                <a href="#">隐私政策</a> |
                <a href="#">商城公益</a> |
                <a href="#">English Site</a>
              </p>
            </div>
            <div class="cert-info">
              <div class="cert-item">
                <img src="/images/cert1.png" alt="认证1" />
              </div>
              <div class="cert-item">
                <img src="/images/cert2.png" alt="认证2" />
              </div>
              <div class="cert-item">
                <img src="/images/cert3.png" alt="认证3" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  </div>
</template>

<script>
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { User, ArrowDown, ArrowRight, ShoppingCart, Van, Clock, Shield, Headset } from '@element-plus/icons-vue'
import api, { cartAPI } from '@/api/user.js'
import { productAPI } from '@/api/product.js'

export default {
  name: 'MallPage',
  components: {
    User,
    ArrowDown,
    ArrowRight,
    ShoppingCart,
    Van,
    Clock,
    Shield,
    Headset
  },
  setup() {
    const router = useRouter()
    
    // 响应式数据
    const searchKeyword = ref('')
    const selectedCategory = ref(null)
    const sortBy = ref('default')
    const currentPage = ref(1)
    const pageSize = ref(12)
    const loading = ref(false)

    // 获取当前用户ID（从localStorage获取）
    const getCurrentUserId = () => {
      try {
        const userInfo = localStorage.getItem('userInfo')
        if (userInfo) {
          const userData = JSON.parse(userInfo)
          return userData.id
        }
        return null
      } catch (error) {
        console.error('获取用户ID失败:', error)
        return null
      }
    }

    // 用户信息
    const userInfo = reactive({
      username: '用户',
      avatar: ''
    })
    
    // 热门搜索关键词
    const hotKeywords = ref(['茅台', '手机', '笔记本', '零食', '护肤品'])
    
    // 商品分类
    const categories = ref([])
    
    // 商品列表
    const products = ref([])
    const totalProducts = ref(0)
    
    // 计算属性
    const filteredProducts = computed(() => {
      let filtered = [...products.value]

      // 按关键词搜索（商品名称和描述）
      if (searchKeyword.value.trim()) {
        const keyword = searchKeyword.value.trim().toLowerCase()
        filtered = filtered.filter(product =>
          product.name.toLowerCase().includes(keyword) ||
          product.description.toLowerCase().includes(keyword) ||
          (product.category && product.category.toLowerCase().includes(keyword))
        )
      }

      // 按分类筛选
      if (selectedCategory.value) {
        filtered = filtered.filter(product =>
          product.categoryId === selectedCategory.value
        )
      }

      // 排序
      switch (sortBy.value) {
        case 'price_asc':
          filtered.sort((a, b) => a.price - b.price)
          break
        case 'price_desc':
          filtered.sort((a, b) => b.price - a.price)
          break
        case 'newest':
          filtered.sort((a, b) => new Date(b.createTime) - new Date(a.createTime))
          break
        default:
          // 默认排序保持原有顺序
          break
      }

      return filtered
    })

    // 分页后的商品
    const paginatedProducts = computed(() => {
      const start = (currentPage.value - 1) * pageSize.value
      const end = start + pageSize.value
      return filteredProducts.value.slice(start, end)
    })

    // 总商品数量
    const totalFilteredProducts = computed(() => {
      return filteredProducts.value.length
    })
    
    // 初始化用户信息
    const initUserInfo = () => {
      const userInfoStr = localStorage.getItem('userInfo')
      if (userInfoStr) {
        const userData = JSON.parse(userInfoStr)
        userInfo.username = userData.username || '用户'
        userInfo.avatar = userData.avatar || ''
      }
    }
    
    // 加载商品分类
    const loadCategories = async () => {
      try {
        const response = await api.get('/product/categories')
        if (response.success) {
          categories.value = response.data
        } else {
          ElMessage.error(response.message || '加载分类失败')
        }
      } catch (error) {
        console.error('加载分类失败:', error)
        ElMessage.error('加载分类失败')
      }
    }
    
    // 加载商品列表
    const loadProducts = async () => {
      loading.value = true
      try {
        const response = await productAPI.getOnlineProducts()  // 只获取上架商品
        console.log('API响应完整数据:', response)

        // 修复数据访问：response.data 是后端返回的数据
        const apiData = response.data
        console.log('后端返回数据:', apiData)

        if (apiData.success) {
          // 处理商品数据，添加前端需要的字段
          products.value = apiData.data.map(product => ({
            id: product.id,
            name: product.name,
            description: product.description,
            price: product.price,
            originalPrice: null, // 数据库中暂无原价字段
            imageUrl: product.image_url,
            categoryId: product.category_id,
            category: product.category_name,
            createTime: product.create_time,
            adding: false
          }))

          totalProducts.value = products.value.length
          console.log('处理后的商品数据:', products.value)
          ElMessage.success(`成功加载 ${products.value.length} 个商品`)
        } else {
          ElMessage.error(apiData.message || '加载商品失败')
          products.value = []
        }
        
      } catch (error) {
        console.error('加载商品失败:', error)
        ElMessage.error('加载商品失败')
      } finally {
        loading.value = false
      }
    }
    
    // 搜索处理
    const handleSearch = () => {
      currentPage.value = 1
      // 搜索时重置到第一页，利用计算属性自动过滤
    }

    // 热门关键词搜索
    const searchByKeyword = (keyword) => {
      searchKeyword.value = keyword
      handleSearch()
    }

    // 选择分类
    const selectCategory = (categoryId) => {
      selectedCategory.value = selectedCategory.value === categoryId ? null : categoryId
      currentPage.value = 1
      // 分类筛选时重置到第一页，利用计算属性自动过滤
    }

    // 排序处理
    const handleSort = () => {
      currentPage.value = 1
      // 排序时重置到第一页，利用计算属性自动排序
    }

    // 清空搜索
    const clearSearch = () => {
      searchKeyword.value = ''
      selectedCategory.value = null
      sortBy.value = 'default'
      currentPage.value = 1
    }
    
    // 分页处理
    const handleSizeChange = (size) => {
      pageSize.value = size
      currentPage.value = 1
      // 利用计算属性自动重新分页
    }

    const handleCurrentChange = (page) => {
      currentPage.value = page
      // 利用计算属性自动重新分页
    }
    
    // 查看商品详情
    const viewProduct = (product) => {
      router.push(`/product/${product.id}`)
    }
    
    // 加入购物车
    const addToCart = async (product) => {
      product.adding = true
      try {
        const userId = getCurrentUserId()

        if (!userId) {
          ElMessage.error('请先登录')
          router.push('/user-login')
          return
        }

        const response = await cartAPI.addToCart(userId, product.id, 1)
        if (response.success) {
          ElMessage({
            message: '商品已成功加入购物车！',
            type: 'success',
            duration: 2000,
            showClose: true
          })
          // 可以在这里更新购物车数量显示
          console.log('购物车信息:', response.cartInfo)
        } else {
          ElMessage.error(response.message || '加入购物车失败')
        }
      } catch (error) {
        console.error('加入购物车失败:', error)
        ElMessage.error('加入购物车失败')
      } finally {
        product.adding = false
      }
    }
    
    // 获取图片URL
    const getImageUrl = (imageUrl) => {
      if (!imageUrl || imageUrl.trim() === '') return ''

      // 如果已经是完整URL，直接返回
      if (imageUrl.startsWith('http')) {
        return imageUrl
      }

      // 如果是相对路径，添加服务器地址
      return `http://localhost:8082${imageUrl}`
    }

    // 图片加载错误处理
    const handleImageError = (event) => {
      event.target.src = '/images/default-product.svg'
    }
    
    // 用户操作处理
    const handleUserCommand = (command) => {
      switch (command) {
        case 'profile':
          router.push('/profile')
          break
        case 'orders':
          router.push('/orders')
          break
        case 'cart':
          router.push('/cart')
          break
        case 'logout':
          localStorage.removeItem('userInfo')
          localStorage.removeItem('isLoggedIn')
          ElMessage.success('已退出登录')
          router.push('/')
          break
      }
    }
    
    // 组件挂载时初始化
    onMounted(() => {
      initUserInfo()
      loadCategories()
      loadProducts()
    })
    
    return {
      searchKeyword,
      selectedCategory,
      sortBy,
      currentPage,
      pageSize,
      loading,
      userInfo,
      hotKeywords,
      categories,
      products,
      totalProducts,
      filteredProducts,
      paginatedProducts,
      totalFilteredProducts,
      handleSearch,
      searchByKeyword,
      selectCategory,
      handleSort,
      clearSearch,
      handleSizeChange,
      handleCurrentChange,
      viewProduct,
      addToCart,
      getImageUrl,
      handleImageError,
      handleUserCommand
    }
  }
}
</script>

<style scoped>
.mall-container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* 顶部导航栏样式 */
.mall-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 1000;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 80px;
}

.logo-section {
  flex-shrink: 0;
}

.logo {
  display: flex;
  align-items: center;
}

.logo-text {
  font-size: 28px;
  font-weight: bold;
  color: white;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

/* 搜索区域样式 */
.search-section {
  flex: 1;
  max-width: 600px;
  margin: 0 40px;
}

.search-container {
  margin-bottom: 8px;
}

.search-input {
  width: 100%;
}

.search-input :deep(.el-input__wrapper) {
  border-radius: 25px 0 0 25px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.search-btn {
  border-radius: 0 25px 25px 0;
  padding: 0 30px;
  font-weight: bold;
}

.hot-keywords {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.hot-label {
  color: white;
  font-size: 12px;
  opacity: 0.9;
}

.hot-tag {
  cursor: pointer;
  font-size: 12px;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
}

.hot-tag:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
}

/* 用户区域样式 */
.user-section {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  gap: 15px;
}

.user-dropdown {
  color: white;
}

.user-dropdown :deep(.el-dropdown-link) {
  color: white;
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.user-dropdown :deep(.el-dropdown-link:hover) {
  background: rgba(255, 255, 255, 0.2);
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: white;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #667eea;
  font-weight: bold;
}

/* 主要内容区域样式 */
.mall-main {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  display: flex;
  gap: 20px;
}

/* 分类侧边栏样式 */
.category-sidebar {
  width: 200px;
  flex-shrink: 0;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  height: fit-content;
  position: sticky;
  top: 100px;
}

.category-title {
  padding: 16px;
  font-size: 16px;
  font-weight: bold;
  color: #333;
  border-bottom: 1px solid #eee;
  background: #f8f9fa;
  border-radius: 8px 8px 0 0;
}

.category-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.category-item {
  padding: 12px 16px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #f0f0f0;
  transition: all 0.3s ease;
}

.category-item:hover {
  background: #f8f9fa;
  color: #667eea;
}

.category-item.active {
  background: #667eea;
  color: white;
}

.category-name {
  font-size: 14px;
}

.category-arrow {
  font-size: 12px;
  opacity: 0.6;
}

/* 商品内容区域样式 */
.products-content {
  flex: 1;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* 搜索条件显示样式 */
.search-conditions {
  padding: 16px 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #eee;
}

.condition-tags {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.condition-label {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.condition-tag {
  margin: 0;
}

.clear-all-btn {
  margin-left: 8px;
}

.products-header {
  padding: 16px 20px;
  border-bottom: 1px solid #eee;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #f8f9fa;
}

.products-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.sort-controls {
  display: flex;
  align-items: center;
  gap: 10px;
}

.sort-label {
  font-size: 14px;
  color: #666;
}

/* 商品网格样式 */
.products-grid {
  padding: 20px;
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
  min-height: 400px;
}

.product-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  overflow: hidden;
  transition: all 0.3s ease;
  cursor: pointer;
  border: 1px solid #f0f0f0;
  height: 320px;
  display: flex;
  flex-direction: column;
}

.product-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
}

.product-image {
  position: relative;
  width: 100%;
  height: 160px;
  overflow: hidden;
  background: #f8f9fa;
  flex-shrink: 0;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.product-card:hover .product-image img {
  transform: scale(1.05);
}

.product-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.product-card:hover .product-overlay {
  opacity: 1;
}

.product-overlay .el-button {
  background: rgba(255, 255, 255, 0.9);
  border: none;
  color: #333;
  font-size: 12px;
  padding: 6px 12px;
  border-radius: 4px;
  backdrop-filter: blur(4px);
}

.product-info {
  padding: 12px;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.product-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin: 0 0 6px 0;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  height: 36px;
}

.product-desc {
  font-size: 12px;
  color: #666;
  margin: 0 0 8px 0;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  height: 16px;
}

.product-price {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 10px;
}

.current-price {
  font-size: 16px;
  font-weight: bold;
  color: #e74c3c;
}

.original-price {
  font-size: 12px;
  color: #999;
  text-decoration: line-through;
}

.product-actions {
  display: flex;
  justify-content: center;
  margin-top: auto;
  padding: 8px 0;
}

.add-to-cart-btn {
  width: 100%;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  padding: 10px 16px;
  height: 36px;
  transition: all 0.3s ease;
}

.add-to-cart-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
}

.product-actions .el-button {
  border-radius: 4px;
  font-size: 12px;
  padding: 6px 8px;
  height: 28px;
}

/* 分页样式 */
.pagination-container {
  padding: 20px;
  display: flex;
  justify-content: center;
  background: white;
  border-top: 1px solid #eee;
}

.pagination-container :deep(.el-pagination) {
  --el-pagination-button-color: #666;
  --el-pagination-hover-color: #667eea;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .products-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 14px;
  }
}

@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    height: auto;
    padding: 15px;
    gap: 15px;
  }

  .search-section {
    margin: 0;
    max-width: 100%;
  }

  .mall-main {
    flex-direction: column;
    padding: 15px;
  }

  .category-sidebar {
    width: 100%;
    position: static;
  }

  .products-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
    padding: 15px;
  }

  .product-card {
    height: 280px;
  }

  .product-image {
    height: 140px;
  }

  .product-title {
    font-size: 13px;
    height: 32px;
  }

  .current-price {
    font-size: 14px;
  }

  .products-header {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }
}

@media (max-width: 480px) {
  .products-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .product-card {
    height: 300px;
    max-width: 100%;
  }

  .product-image {
    height: 160px;
  }

  .product-title {
    font-size: 14px;
    height: 36px;
  }

  .current-price {
    font-size: 16px;
  }
}

/* 加载状态样式 */
.products-grid.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
}

.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  color: #666;
}

/* 空状态样式 */
.empty-state {
  text-align: center;
  padding: 80px 20px;
  color: #999;
  grid-column: 1 / -1;
}

.empty-state .el-icon {
  font-size: 64px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-state h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  color: #666;
}

.empty-state p {
  margin: 0;
  font-size: 14px;
  color: #999;
}

/* 商品卡片骨架屏 */
.product-skeleton {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  overflow: hidden;
  height: 320px;
  display: flex;
  flex-direction: column;
}

.skeleton-image {
  width: 100%;
  height: 160px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
}

.skeleton-content {
  padding: 12px;
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.skeleton-line {
  height: 12px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 4px;
}

.skeleton-line.title {
  height: 16px;
  width: 80%;
}

.skeleton-line.desc {
  height: 12px;
  width: 60%;
}

.skeleton-line.price {
  height: 14px;
  width: 40%;
  margin-top: auto;
}

@keyframes skeleton-loading {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* 京东风格底部页脚 */
.mall-footer {
  background: white;
  border-top: 1px solid var(--jd-border);
  margin-top: 40px;
}

/* 服务承诺区域 */
.service-promises {
  background: #f8f8f8;
  padding: 20px 0;
  border-bottom: 1px solid var(--jd-border);
}

.footer-content {
  max-width: 1210px;
  margin: 0 auto;
  padding: 0 15px;
}

.service-promises .footer-content {
  display: flex;
  justify-content: space-around;
  align-items: center;
}

.promise-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.promise-icon {
  width: 40px;
  height: 40px;
  background: var(--jd-red);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 18px;
}

.promise-text {
  display: flex;
  flex-direction: column;
}

.promise-title {
  font-size: 14px;
  font-weight: bold;
  color: var(--jd-text-primary);
  margin-bottom: 2px;
}

.promise-desc {
  font-size: 12px;
  color: var(--jd-text-secondary);
}

/* 主要信息区域 */
.footer-main {
  background: white;
  padding: 30px 0;
}

.footer-main .footer-content {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 30px;
}

.footer-section {
  display: flex;
  flex-direction: column;
}

.section-title {
  font-size: 14px;
  font-weight: bold;
  color: var(--jd-text-primary);
  margin: 0 0 15px 0;
  padding-bottom: 8px;
  border-bottom: 2px solid var(--jd-red);
  width: fit-content;
}

.section-links {
  list-style: none;
  padding: 0;
  margin: 0;
}

.section-links li {
  margin-bottom: 8px;
}

.section-links a {
  color: var(--jd-text-secondary);
  text-decoration: none;
  font-size: 12px;
  transition: color 0.2s ease;
}

.section-links a:hover {
  color: var(--jd-red);
}

/* 管理员登录特殊样式 */
.admin-section .section-title {
  border-bottom-color: var(--jd-orange);
}

.admin-link {
  color: var(--jd-red) !important;
  font-weight: bold;
}

.admin-link:hover {
  color: var(--jd-red-hover) !important;
}

/* 版权信息区域 */
.footer-bottom {
  background: #f0f0f0;
  padding: 20px 0;
  border-top: 1px solid var(--jd-border);
}

.copyright-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.company-info p {
  margin: 5px 0;
  font-size: 12px;
  color: var(--jd-text-secondary);
}

.company-info a {
  color: var(--jd-text-secondary);
  text-decoration: none;
  margin: 0 5px;
}

.company-info a:hover {
  color: var(--jd-red);
}

.cert-info {
  display: flex;
  gap: 10px;
  align-items: center;
}

.cert-item {
  width: 60px;
  height: 40px;
  background: #f8f8f8;
  border: 1px solid var(--jd-border);
  border-radius: 3px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  color: var(--jd-text-light);
}

.cert-item img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

/* 底部页脚响应式设计 */
@media (max-width: 1200px) {
  .footer-main .footer-content {
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
  }
}

@media (max-width: 768px) {
  .service-promises .footer-content {
    flex-direction: column;
    gap: 15px;
  }

  .promise-item {
    flex-direction: column;
    text-align: center;
    gap: 8px;
  }

  .footer-main .footer-content {
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
  }

  .copyright-info {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }

  .cert-info {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .footer-main .footer-content {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .service-promises {
    padding: 15px 0;
  }

  .footer-main {
    padding: 20px 0;
  }

  .footer-bottom {
    padding: 15px 0;
  }
}
</style>
