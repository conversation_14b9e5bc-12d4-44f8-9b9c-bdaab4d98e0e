<template>
  <div class="cart-container">
    <!-- 顶部导航 -->
    <header class="cart-header">
      <div class="header-content">
        <el-button 
          type="primary" 
          :icon="ArrowLeft" 
          @click="goBack"
          class="back-btn"
        >
          返回商城
        </el-button>
        <h1 class="page-title">购物车</h1>
        <div class="header-actions">
          <span class="cart-count">共{{ cartItems.length }}件商品</span>
        </div>
      </div>
    </header>

    <!-- 购物车内容 -->
    <main class="cart-main">
      <div class="cart-content" v-if="cartItems.length > 0">
        <!-- 购物车商品列表 -->
        <div class="cart-items">
          <div class="cart-header-row">
            <el-checkbox 
              v-model="selectAll" 
              @change="handleSelectAll"
              class="select-all-checkbox"
            >
              全选
            </el-checkbox>
            <span class="item-info-header">商品信息</span>
            <span class="price-header">单价</span>
            <span class="quantity-header">数量</span>
            <span class="total-header">小计</span>
            <span class="action-header">操作</span>
          </div>

          <div 
            v-for="item in cartItems" 
            :key="item.id"
            class="cart-item"
          >
            <el-checkbox 
              v-model="item.selected" 
              @change="updateSelectAll"
              class="item-checkbox"
            />
            
            <div class="item-info">
              <div class="item-image">
                <img
                  :src="getImageUrl(item.imageUrl) || '/images/default-product.svg'"
                  :alt="item.name"
                  @error="handleImageError"
                />
              </div>
              <div class="item-details">
                <h3 class="item-name">{{ item.name }}</h3>
                <p class="item-desc">{{ item.description }}</p>
              </div>
            </div>

            <div class="item-price">
              <span class="current-price">¥{{ item.price }}</span>
              <span class="original-price" v-if="item.originalPrice">¥{{ item.originalPrice }}</span>
            </div>

            <div class="item-quantity">
              <el-input-number
                v-model="item.quantity"
                :min="1"
                :max="99"
                @change="updateItemQuantity(item)"
                size="small"
              />
            </div>

            <div class="item-total">
              <span class="total-price">¥{{ (item.price * item.quantity).toFixed(2) }}</span>
            </div>

            <div class="item-actions">
              <el-button 
                type="danger" 
                size="small" 
                :icon="Delete"
                @click="removeItem(item)"
                link
              >
                删除
              </el-button>
            </div>
          </div>
        </div>

        <!-- 购物车底部结算 -->
        <div class="cart-footer">
          <div class="footer-left">
            <el-checkbox 
              v-model="selectAll" 
              @change="handleSelectAll"
            >
              全选
            </el-checkbox>
            <el-button 
              type="danger" 
              @click="clearSelected"
              :disabled="selectedItems.length === 0"
              link
            >
              删除选中商品
            </el-button>
          </div>
          
          <div class="footer-right">
            <div class="total-info">
              <span class="selected-count">已选择{{ selectedItems.length }}件商品</span>
              <div class="total-amount">
                <span class="total-label">合计：</span>
                <span class="total-price">¥{{ totalAmount }}</span>
              </div>
            </div>
            <el-button 
              type="danger" 
              size="large"
              @click="checkout"
              :disabled="selectedItems.length === 0"
              class="checkout-btn"
            >
              结算({{ selectedItems.length }})
            </el-button>
          </div>
        </div>
      </div>

      <!-- 空购物车状态 -->
      <div class="empty-cart" v-else>
        <div class="empty-icon">
          <el-icon size="80"><ShoppingCart /></el-icon>
        </div>
        <h3>购物车是空的</h3>
        <p>快去挑选心仪的商品吧~</p>
        <el-button type="primary" @click="goShopping">去购物</el-button>
      </div>
    </main>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowLeft, Delete, ShoppingCart } from '@element-plus/icons-vue'
import { cartAPI, ordersAPI } from '@/api/user.js'

export default {
  name: 'CartPage',
  components: {
    ArrowLeft,
    Delete,
    ShoppingCart
  },
  setup() {
    const router = useRouter()
    
    // 响应式数据
    const cartItems = ref([])
    const selectAll = ref(false)

    // 获取当前用户ID（从localStorage获取）
    const getCurrentUserId = () => {
      try {
        const userInfo = localStorage.getItem('userInfo')
        if (userInfo) {
          const userData = JSON.parse(userInfo)
          return userData.id
        }
        return null
      } catch (error) {
        console.error('获取用户ID失败:', error)
        return null
      }
    }

    // 计算属性
    const selectedItems = computed(() => {
      return cartItems.value.filter(item => item.selected)
    })
    
    const totalAmount = computed(() => {
      return selectedItems.value.reduce((total, item) => {
        return total + (item.price * item.quantity)
      }, 0).toFixed(2)
    })
    
    // 加载购物车数据
    const loadCartItems = async () => {
      try {
        const userId = getCurrentUserId()

        if (!userId) {
          ElMessage.error('用户未登录，请先登录')
          router.push('/user-login')
          return
        }

        const response = await cartAPI.getCart(userId)
        if (response.success) {
          // 处理购物车数据，添加前端需要的字段
          cartItems.value = response.data.map(item => ({
            id: item.id,                    // 购物车项ID
            productId: item.product_id,     // 商品ID
            name: item.name,                // 商品名称
            description: item.description,  // 商品描述
            price: item.price,              // 商品价格
            originalPrice: null,            // 原价（数据库中暂无此字段）
            quantity: item.quantity,        // 购买数量
            imageUrl: item.image_url,       // 商品图片
            categoryName: item.category_name, // 分类名称
            selected: false                 // 是否选中（用于结算）
          }))
        } else {
          ElMessage.error(response.message || '加载购物车失败')
          cartItems.value = []
        }
      } catch (error) {
        console.error('加载购物车失败:', error)
        ElMessage.error('加载购物车失败')
        cartItems.value = []
      }
    }
    
    // 全选/取消全选
    const handleSelectAll = (checked) => {
      cartItems.value.forEach(item => {
        item.selected = checked
      })
    }
    
    // 更新全选状态
    const updateSelectAll = () => {
      selectAll.value = cartItems.value.length > 0 && cartItems.value.every(item => item.selected)
    }
    
    // 更新商品数量
    const updateItemQuantity = async (item) => {
      try {
        const response = await cartAPI.updateQuantity(item.id, item.quantity)
        if (response.success) {
          ElMessage.success('更新数量成功')
        } else {
          ElMessage.error(response.message || '更新数量失败')
          // 恢复原来的数量
          loadCartItems()
        }
      } catch (error) {
        console.error('更新数量失败:', error)
        ElMessage.error('更新数量失败')
        // 恢复原来的数量
        loadCartItems()
      }
    }
    
    // 删除单个商品
    const removeItem = async (item) => {
      try {
        await ElMessageBox.confirm('确定要删除这件商品吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        const userId = getCurrentUserId()

        if (!userId) {
          ElMessage.error('用户未登录，请先登录')
          router.push('/user-login')
          return
        }

        const response = await cartAPI.removeFromCart(userId, item.productId)
        if (response.success) {
          ElMessage.success('删除成功')
          // 重新加载购物车数据
          loadCartItems()
        } else {
          ElMessage.error(response.message || '删除失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除失败:', error)
          ElMessage.error('删除失败')
        }
      }
    }
    
    // 删除选中商品
    const clearSelected = async () => {
      try {
        await ElMessageBox.confirm(`确定要删除选中的${selectedItems.value.length}件商品吗？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        const userId = getCurrentUserId()

        if (!userId) {
          ElMessage.error('用户未登录，请先登录')
          router.push('/user-login')
          return
        }

        // 获取选中的商品ID列表
        const productIds = selectedItems.value.map(item => item.productId)

        const response = await cartAPI.batchRemoveFromCart(userId, productIds)
        if (response.success) {
          ElMessage.success('删除成功')
          // 重新加载购物车数据
          loadCartItems()
          selectAll.value = false
        } else {
          ElMessage.error(response.message || '删除失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除失败:', error)
          ElMessage.error('删除失败')
        }
      }
    }
    
    // 结算
    const checkout = async () => {
      if (selectedItems.value.length === 0) {
        ElMessage.warning('请选择要结算的商品')
        return
      }

      try {
        const userId = getCurrentUserId()

        if (!userId) {
          ElMessage.error('用户未登录，请先登录')
          router.push('/user-login')
          return
        }

        // 获取选中的购物车项ID
        const cartItemIds = selectedItems.value.map(item => item.id)

        // 创建订单
        const response = await ordersAPI.createOrderFromCart(userId, cartItemIds)
        if (response.success) {
          const order = response.data
          ElMessage.success('订单创建成功')

          // 跳转到支付页面
          const payUrl = `http://localhost:8082/api/alipay/pay?subject=${encodeURIComponent('商城订单支付')}&traceNo=${order.orderNo}&totalAmount=${order.totalAmount}`

          // 创建一个新的窗口来显示支付页面
          const payWindow = window.open('', '_blank')

          // 获取支付表单HTML
          try {
            const payResponse = await fetch(payUrl, {
              method: 'GET'
            })
            const payHtml = await payResponse.text()

            // 在新窗口中写入支付表单HTML
            payWindow.document.write(payHtml)
            payWindow.document.close()
          } catch (error) {
            console.error('获取支付页面失败:', error)
            payWindow.close()
            ElMessage.error('跳转支付页面失败')
          }

          // 重新加载购物车数据
          loadCartItems()
        } else {
          ElMessage.error(response.message || '订单创建失败')
        }
      } catch (error) {
        console.error('结算失败:', error)
        ElMessage.error('结算失败')
      }
    }
    
    // 返回商城
    const goBack = () => {
      router.push('/mall')
    }
    
    // 去购物
    const goShopping = () => {
      router.push('/mall')
    }
    
    // 获取图片URL
    const getImageUrl = (imageUrl) => {
      if (!imageUrl || imageUrl.trim() === '') return ''

      // 如果已经是完整URL，直接返回
      if (imageUrl.startsWith('http')) {
        return imageUrl
      }

      // 如果是相对路径，添加服务器地址
      return `http://localhost:8082${imageUrl}`
    }

    // 图片加载错误处理
    const handleImageError = (event) => {
      event.target.src = '/images/default-product.svg'
    }
    
    // 组件挂载时加载数据
    onMounted(() => {
      loadCartItems()
    })
    
    return {
      cartItems,
      selectAll,
      selectedItems,
      totalAmount,
      handleSelectAll,
      updateSelectAll,
      updateItemQuantity,
      removeItem,
      clearSelected,
      checkout,
      goBack,
      goShopping,
      getImageUrl,
      handleImageError
    }
  }
}
</script>

<style scoped>
.cart-container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* 顶部导航样式 */
.cart-header {
  background: linear-gradient(135deg, #ff6a00 0%, #ff8533 50%, #ffb366 100%);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 1000;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 70px;
}

.back-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
}

.back-btn:hover {
  background: rgba(255, 255, 255, 0.3);
}

.page-title {
  color: white;
  font-size: 20px;
  font-weight: bold;
  margin: 0;
}

.cart-count {
  color: white;
  font-size: 14px;
  opacity: 0.9;
}

/* 主要内容区域 */
.cart-main {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.cart-content {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

/* 购物车表头 */
.cart-header-row {
  display: grid;
  grid-template-columns: 50px 1fr 120px 120px 120px 80px;
  gap: 20px;
  align-items: center;
  padding: 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #eee;
  font-weight: 500;
  color: #666;
}

.select-all-checkbox {
  justify-self: center;
}

/* 购物车商品项 */
.cart-item {
  display: grid;
  grid-template-columns: 50px 1fr 120px 120px 120px 80px;
  gap: 20px;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.3s ease;
}

.cart-item:hover {
  background: #f8f9fa;
}

.item-checkbox {
  justify-self: center;
}

.item-info {
  display: flex;
  align-items: center;
  gap: 15px;
}

.item-image {
  width: 80px;
  height: 80px;
  border-radius: 8px;
  overflow: hidden;
  background: #f8f9fa;
  flex-shrink: 0;
}

.item-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.item-details {
  flex: 1;
  min-width: 0;
}

.item-name {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin: 0 0 8px 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.item-desc {
  font-size: 13px;
  color: #666;
  margin: 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.item-price {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.current-price {
  font-size: 16px;
  font-weight: bold;
  color: #e74c3c;
}

.original-price {
  font-size: 12px;
  color: #999;
  text-decoration: line-through;
}

.item-quantity {
  display: flex;
  justify-content: center;
}

.item-total {
  display: flex;
  justify-content: center;
}

.total-price {
  font-size: 16px;
  font-weight: bold;
  color: #e74c3c;
}

.item-actions {
  display: flex;
  justify-content: center;
}

/* 购物车底部 */
.cart-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  background: #f8f9fa;
  border-top: 1px solid #eee;
}

.footer-left {
  display: flex;
  align-items: center;
  gap: 20px;
}

.footer-right {
  display: flex;
  align-items: center;
  gap: 20px;
}

.total-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 5px;
}

.selected-count {
  font-size: 14px;
  color: #666;
}

.total-amount {
  display: flex;
  align-items: center;
  gap: 5px;
}

.total-label {
  font-size: 16px;
  color: #333;
}

.total-amount .total-price {
  font-size: 24px;
  font-weight: bold;
  color: #e74c3c;
}

.checkout-btn {
  padding: 12px 30px;
  font-size: 16px;
  font-weight: bold;
  border-radius: 8px;
  background: linear-gradient(135deg, #ff6a00, #ff8533) !important;
  border: none !important;
  color: white !important;
  transition: all 0.3s ease;
}

.checkout-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #ff5500, #ff6a00) !important;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(255, 106, 0, 0.4);
}

/* 空购物车状态 */
.empty-cart {
  text-align: center;
  padding: 80px 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.empty-icon {
  margin-bottom: 20px;
  color: #ccc;
}

.empty-cart h3 {
  font-size: 20px;
  color: #666;
  margin: 0 0 10px 0;
}

.empty-cart p {
  font-size: 14px;
  color: #999;
  margin: 0 0 30px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-content {
    padding: 0 15px;
  }

  .cart-main {
    padding: 15px;
  }

  .cart-header-row,
  .cart-item {
    grid-template-columns: 40px 1fr 80px 80px 80px 60px;
    gap: 10px;
    padding: 15px;
  }

  .item-image {
    width: 60px;
    height: 60px;
  }

  .item-name {
    font-size: 14px;
  }

  .item-desc {
    font-size: 12px;
  }

  .current-price,
  .total-price {
    font-size: 14px;
  }

  .cart-footer {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }

  .footer-right {
    justify-content: space-between;
  }

  .total-amount .total-price {
    font-size: 20px;
  }
}

@media (max-width: 480px) {
  .cart-header-row {
    display: none;
  }

  .cart-item {
    display: flex;
    flex-direction: column;
    gap: 15px;
    padding: 15px;
  }

  .item-checkbox {
    align-self: flex-start;
  }

  .item-info {
    width: 100%;
  }

  .item-image {
    width: 80px;
    height: 80px;
  }

  .item-price,
  .item-quantity,
  .item-total,
  .item-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    padding: 8px 0;
    border-top: 1px solid #f0f0f0;
  }

  .item-price::before {
    content: "单价：";
    color: #666;
    font-size: 14px;
  }

  .item-quantity::before {
    content: "数量：";
    color: #666;
    font-size: 14px;
  }

  .item-total::before {
    content: "小计：";
    color: #666;
    font-size: 14px;
  }

  .item-actions::before {
    content: "操作：";
    color: #666;
    font-size: 14px;
  }
}
</style>
