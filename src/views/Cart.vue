<template>
  <div class="cart-container">
    <!-- 顶部导航 -->
    <header class="cart-header">
      <div class="header-content">
        <el-button 
          type="primary" 
          :icon="ArrowLeft" 
          @click="goBack"
          class="back-btn"
        >
          返回商城
        </el-button>
        <h1 class="page-title">购物车</h1>
        <div class="header-actions">
          <span class="cart-count">共{{ cartItems.length }}件商品</span>
        </div>
      </div>
    </header>

    <!-- 购物车内容 -->
    <main class="cart-main">
      <div class="cart-content" v-if="cartItems.length > 0">
        <!-- 购物车商品列表 -->
        <div class="cart-items">
          <div class="cart-header-row">
            <el-checkbox 
              v-model="selectAll" 
              @change="handleSelectAll"
              class="select-all-checkbox"
            >
              全选
            </el-checkbox>
            <span class="item-info-header">商品信息</span>
            <span class="price-header">单价</span>
            <span class="quantity-header">数量</span>
            <span class="total-header">小计</span>
            <span class="action-header">操作</span>
          </div>

          <div 
            v-for="item in cartItems" 
            :key="item.id"
            class="cart-item"
          >
            <el-checkbox 
              v-model="item.selected" 
              @change="updateSelectAll"
              class="item-checkbox"
            />
            
            <div class="item-info">
              <div class="item-image">
                <img
                  :src="getImageUrl(item.imageUrl) || '/images/default-product.svg'"
                  :alt="item.name"
                  @error="handleImageError"
                />
              </div>
              <div class="item-details">
                <h3 class="item-name">{{ item.name }}</h3>
                <p class="item-desc">{{ item.description }}</p>
              </div>
            </div>

            <div class="item-price">
              <span class="current-price">¥{{ item.price }}</span>
              <span class="original-price" v-if="item.originalPrice">¥{{ item.originalPrice }}</span>
            </div>

            <div class="item-quantity">
              <el-input-number
                v-model="item.quantity"
                :min="1"
                :max="99"
                @change="updateItemQuantity(item)"
                size="small"
              />
            </div>

            <div class="item-total">
              <span class="total-price">¥{{ (item.price * item.quantity).toFixed(2) }}</span>
            </div>

            <div class="item-actions">
              <el-button 
                type="danger" 
                size="small" 
                :icon="Delete"
                @click="removeItem(item)"
                link
              >
                删除
              </el-button>
            </div>
          </div>
        </div>

        <!-- 购物车底部结算 -->
        <div class="cart-footer">
          <div class="footer-left">
            <el-checkbox 
              v-model="selectAll" 
              @change="handleSelectAll"
            >
              全选
            </el-checkbox>
            <el-button 
              type="danger" 
              @click="clearSelected"
              :disabled="selectedItems.length === 0"
              link
            >
              删除选中商品
            </el-button>
          </div>
          
          <div class="footer-right">
            <div class="total-info">
              <span class="selected-count">已选择{{ selectedItems.length }}件商品</span>
              <div class="total-amount">
                <span class="total-label">合计：</span>
                <span class="total-price">¥{{ totalAmount }}</span>
              </div>
            </div>
            <el-button 
              type="danger" 
              size="large"
              @click="checkout"
              :disabled="selectedItems.length === 0"
              class="checkout-btn"
            >
              结算({{ selectedItems.length }})
            </el-button>
          </div>
        </div>
      </div>

      <!-- 空购物车状态 -->
      <div class="empty-cart" v-else>
        <div class="empty-icon">
          <el-icon size="80"><ShoppingCart /></el-icon>
        </div>
        <h3>购物车是空的</h3>
        <p>快去挑选心仪的商品吧~</p>
        <el-button type="primary" @click="goShopping">去购物</el-button>
      </div>
    </main>
  </div>
</template>

<script>
/* eslint-disable vue/no-unused-components */
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowLeft, Delete, ShoppingCart } from '@element-plus/icons-vue'

export default {
  name: 'Cart',
  components: {
    ArrowLeft,
    Delete,
    ShoppingCart
  },
  setup() {
    const router = useRouter()
    
    // 响应式数据
    const cartItems = ref([])
    const selectAll = ref(false)
    
    // 计算属性
    const selectedItems = computed(() => {
      return cartItems.value.filter(item => item.selected)
    })
    
    const totalAmount = computed(() => {
      return selectedItems.value.reduce((total, item) => {
        return total + (item.price * item.quantity)
      }, 0).toFixed(2)
    })
    
    // 加载购物车数据
    const loadCartItems = async () => {
      try {
        // TODO: 调用API获取购物车数据
        await new Promise(resolve => setTimeout(resolve, 500)) // 模拟请求
        
        // 模拟购物车数据
        cartItems.value = [
          {
            id: 1,
            name: '茅台（MOUTAI）飞天 酱香型白酒 53度 200ml 单瓶装',
            description: '茅台（MOUTAI）飞天 酱香型白酒 53度 200ml 单瓶装，正宗茅台酒，酱香浓郁',
            price: 299.00,
            originalPrice: 350.00,
            quantity: 2,
            imageUrl: '/images/maotai_200ml.jpg',
            selected: false
          },
          {
            id: 2,
            name: '五粮液 52度 500ml 浓香型白酒',
            description: '五粮液经典装，五种粮食酿造，口感醇厚',
            price: 899.00,
            quantity: 1,
            imageUrl: '/images/wuliangye_500ml.jpg',
            selected: false
          }
        ]
        
      } catch (error) {
        console.error('加载购物车失败:', error)
        ElMessage.error('加载购物车失败')
      }
    }
    
    // 全选/取消全选
    const handleSelectAll = (checked) => {
      cartItems.value.forEach(item => {
        item.selected = checked
      })
    }
    
    // 更新全选状态
    const updateSelectAll = () => {
      selectAll.value = cartItems.value.length > 0 && cartItems.value.every(item => item.selected)
    }
    
    // 更新商品数量
    const updateItemQuantity = async (item) => {
      try {
        // TODO: 调用API更新购物车商品数量
        console.log('更新商品数量:', item)
      } catch (error) {
        ElMessage.error('更新失败')
      }
    }
    
    // 删除单个商品
    const removeItem = async (item) => {
      try {
        await ElMessageBox.confirm('确定要删除这件商品吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        
        const index = cartItems.value.findIndex(cartItem => cartItem.id === item.id)
        if (index > -1) {
          cartItems.value.splice(index, 1)
          ElMessage.success('删除成功')
          updateSelectAll()
        }
      } catch (error) {
        // 用户取消删除
      }
    }
    
    // 删除选中商品
    const clearSelected = async () => {
      try {
        await ElMessageBox.confirm(`确定要删除选中的${selectedItems.value.length}件商品吗？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        
        cartItems.value = cartItems.value.filter(item => !item.selected)
        ElMessage.success('删除成功')
        selectAll.value = false
      } catch (error) {
        // 用户取消删除
      }
    }
    
    // 结算
    const checkout = () => {
      if (selectedItems.value.length === 0) {
        ElMessage.warning('请选择要结算的商品')
        return
      }
      
      // TODO: 跳转到订单确认页面
      ElMessage.info('结算功能开发中...')
    }
    
    // 返回商城
    const goBack = () => {
      router.push('/mall')
    }
    
    // 去购物
    const goShopping = () => {
      router.push('/mall')
    }
    
    // 获取图片URL
    const getImageUrl = (imageUrl) => {
      if (!imageUrl || imageUrl.trim() === '') return ''

      // 如果已经是完整URL，直接返回
      if (imageUrl.startsWith('http')) {
        return imageUrl
      }

      // 如果是相对路径，添加服务器地址
      return `http://localhost:8082${imageUrl}`
    }

    // 图片加载错误处理
    const handleImageError = (event) => {
      event.target.src = '/images/default-product.svg'
    }
    
    // 组件挂载时加载数据
    onMounted(() => {
      loadCartItems()
    })
    
    return {
      cartItems,
      selectAll,
      selectedItems,
      totalAmount,
      handleSelectAll,
      updateSelectAll,
      updateItemQuantity,
      removeItem,
      clearSelected,
      checkout,
      goBack,
      goShopping,
      getImageUrl,
      handleImageError
    }
  }
}
</script>

<style scoped>
.cart-container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* 顶部导航样式 */
.cart-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 1000;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 70px;
}

.back-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
}

.back-btn:hover {
  background: rgba(255, 255, 255, 0.3);
}

.page-title {
  color: white;
  font-size: 20px;
  font-weight: bold;
  margin: 0;
}

.cart-count {
  color: white;
  font-size: 14px;
  opacity: 0.9;
}

/* 主要内容区域 */
.cart-main {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.cart-content {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

/* 购物车表头 */
.cart-header-row {
  display: grid;
  grid-template-columns: 50px 1fr 120px 120px 120px 80px;
  gap: 20px;
  align-items: center;
  padding: 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #eee;
  font-weight: 500;
  color: #666;
}

.select-all-checkbox {
  justify-self: center;
}

/* 购物车商品项 */
.cart-item {
  display: grid;
  grid-template-columns: 50px 1fr 120px 120px 120px 80px;
  gap: 20px;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.3s ease;
}

.cart-item:hover {
  background: #f8f9fa;
}

.item-checkbox {
  justify-self: center;
}

.item-info {
  display: flex;
  align-items: center;
  gap: 15px;
}

.item-image {
  width: 80px;
  height: 80px;
  border-radius: 8px;
  overflow: hidden;
  background: #f8f9fa;
  flex-shrink: 0;
}

.item-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.item-details {
  flex: 1;
  min-width: 0;
}

.item-name {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin: 0 0 8px 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.item-desc {
  font-size: 13px;
  color: #666;
  margin: 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.item-price {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.current-price {
  font-size: 16px;
  font-weight: bold;
  color: #e74c3c;
}

.original-price {
  font-size: 12px;
  color: #999;
  text-decoration: line-through;
}

.item-quantity {
  display: flex;
  justify-content: center;
}

.item-total {
  display: flex;
  justify-content: center;
}

.total-price {
  font-size: 16px;
  font-weight: bold;
  color: #e74c3c;
}

.item-actions {
  display: flex;
  justify-content: center;
}

/* 购物车底部 */
.cart-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  background: #f8f9fa;
  border-top: 1px solid #eee;
}

.footer-left {
  display: flex;
  align-items: center;
  gap: 20px;
}

.footer-right {
  display: flex;
  align-items: center;
  gap: 20px;
}

.total-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 5px;
}

.selected-count {
  font-size: 14px;
  color: #666;
}

.total-amount {
  display: flex;
  align-items: center;
  gap: 5px;
}

.total-label {
  font-size: 16px;
  color: #333;
}

.total-amount .total-price {
  font-size: 24px;
  font-weight: bold;
  color: #e74c3c;
}

.checkout-btn {
  padding: 12px 30px;
  font-size: 16px;
  font-weight: bold;
  border-radius: 8px;
}

/* 空购物车状态 */
.empty-cart {
  text-align: center;
  padding: 80px 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.empty-icon {
  margin-bottom: 20px;
  color: #ccc;
}

.empty-cart h3 {
  font-size: 20px;
  color: #666;
  margin: 0 0 10px 0;
}

.empty-cart p {
  font-size: 14px;
  color: #999;
  margin: 0 0 30px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-content {
    padding: 0 15px;
  }

  .cart-main {
    padding: 15px;
  }

  .cart-header-row,
  .cart-item {
    grid-template-columns: 40px 1fr 80px 80px 80px 60px;
    gap: 10px;
    padding: 15px;
  }

  .item-image {
    width: 60px;
    height: 60px;
  }

  .item-name {
    font-size: 14px;
  }

  .item-desc {
    font-size: 12px;
  }

  .current-price,
  .total-price {
    font-size: 14px;
  }

  .cart-footer {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }

  .footer-right {
    justify-content: space-between;
  }

  .total-amount .total-price {
    font-size: 20px;
  }
}

@media (max-width: 480px) {
  .cart-header-row {
    display: none;
  }

  .cart-item {
    display: flex;
    flex-direction: column;
    gap: 15px;
    padding: 15px;
  }

  .item-checkbox {
    align-self: flex-start;
  }

  .item-info {
    width: 100%;
  }

  .item-image {
    width: 80px;
    height: 80px;
  }

  .item-price,
  .item-quantity,
  .item-total,
  .item-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    padding: 8px 0;
    border-top: 1px solid #f0f0f0;
  }

  .item-price::before {
    content: "单价：";
    color: #666;
    font-size: 14px;
  }

  .item-quantity::before {
    content: "数量：";
    color: #666;
    font-size: 14px;
  }

  .item-total::before {
    content: "小计：";
    color: #666;
    font-size: 14px;
  }

  .item-actions::before {
    content: "操作：";
    color: #666;
    font-size: 14px;
  }
}
</style>
