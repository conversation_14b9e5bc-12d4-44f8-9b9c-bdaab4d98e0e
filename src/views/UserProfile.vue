<template>
  <div class="profile-container">
    <!-- 顶部导航 -->
    <header class="profile-header">
      <div class="header-content">
        <el-button 
          type="primary" 
          :icon="ArrowLeft" 
          @click="goBack"
          class="back-btn"
        >
          返回商城
        </el-button>
        <h1 class="page-title">个人中心</h1>
        <div class="header-actions">
          <el-button 
            type="danger" 
            @click="logout"
            class="logout-btn"
          >
            退出登录
          </el-button>
        </div>
      </div>
    </header>

    <!-- 主要内容 -->
    <main class="profile-main">
      <div class="profile-content">
        <!-- 加载状态 -->
        <div v-if="loading" class="loading-container">
          <el-skeleton :rows="8" animated />
        </div>

        <!-- 个人信息内容 -->
        <div v-else class="profile-sections">
          <!-- 用户基本信息卡片 -->
          <div class="info-card">
            <div class="card-header">
              <h2>基本信息</h2>
              <el-button 
                type="primary" 
                size="small"
                @click="showEditDialog = true"
              >
                编辑信息
              </el-button>
            </div>
            
            <div class="user-info">
              <div class="avatar-section">
                <el-avatar 
                  :size="80" 
                  :src="userInfo.avatar || ''"
                  class="user-avatar"
                >
                  <el-icon><User /></el-icon>
                </el-avatar>
                <div class="user-basic">
                  <h3 class="username">{{ userInfo.username }}</h3>
                  <p class="user-status">
                    <el-tag :type="userInfo.status === 1 ? 'success' : 'danger'" size="small">
                      {{ userInfo.status === 1 ? '正常用户' : '账户异常' }}
                    </el-tag>
                  </p>
                </div>
              </div>
              
              <div class="info-details">
                <div class="info-row">
                  <span class="label">用户ID：</span>
                  <span class="value">{{ userInfo.id }}</span>
                </div>
                <div class="info-row">
                  <span class="label">邮箱：</span>
                  <span class="value">{{ userInfo.email || '未设置' }}</span>
                </div>
                <div class="info-row">
                  <span class="label">手机号：</span>
                  <span class="value">{{ userInfo.phone || '未设置' }}</span>
                </div>
                <div class="info-row">
                  <span class="label">注册时间：</span>
                  <span class="value">{{ formatTime(userInfo.createTime) }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 账户安全卡片 -->
          <div class="security-card">
            <div class="card-header">
              <h2>账户安全</h2>
            </div>
            
            <div class="security-items">
              <div class="security-item">
                <div class="item-info">
                  <el-icon class="item-icon"><Lock /></el-icon>
                  <div class="item-details">
                    <h4>登录密码</h4>
                    <p>定期更换密码，保护账户安全</p>
                  </div>
                </div>
                <el-button
                  type="primary"
                  size="small"
                  @click="showPasswordDialog = true"
                  class="password-btn"
                >
                  修改密码
                </el-button>
              </div>
            </div>
          </div>

          <!-- 快捷操作卡片 -->
          <div class="actions-card">
            <div class="card-header">
              <h2>快捷操作</h2>
            </div>
            
            <div class="action-buttons">
              <el-button 
                type="primary" 
                :icon="ShoppingBag"
                @click="goToOrders"
                class="action-btn"
              >
                我的订单
              </el-button>
              <el-button 
                type="success" 
                :icon="ShoppingCart"
                @click="goToCart"
                class="action-btn"
              >
                购物车
              </el-button>
              <el-button 
                type="info" 
                :icon="House"
                @click="goToMall"
                class="action-btn"
              >
                返回商城
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </main>

    <!-- 编辑信息对话框 -->
    <el-dialog
      v-model="showEditDialog"
      title="编辑个人信息"
      width="500px"
      :before-close="handleEditClose"
    >
      <el-form
        ref="editFormRef"
        :model="editForm"
        :rules="editRules"
        label-width="80px"
      >
        <el-form-item label="用户名" prop="username">
          <el-input v-model="editForm.username" disabled />
          <div class="form-tip">用户名不可修改</div>
        </el-form-item>
        
        <el-form-item label="邮箱" prop="email">
          <el-input 
            v-model="editForm.email" 
            placeholder="请输入邮箱地址"
            type="email"
          />
        </el-form-item>
        
        <el-form-item label="手机号" prop="phone">
          <el-input 
            v-model="editForm.phone" 
            placeholder="请输入手机号码"
            maxlength="11"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleEditClose">取消</el-button>
          <el-button 
            type="primary" 
            @click="handleEditSubmit"
            :loading="editLoading"
          >
            保存
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 修改密码对话框 -->
    <el-dialog
      v-model="showPasswordDialog"
      title="修改密码"
      width="500px"
      :before-close="handlePasswordClose"
    >
      <el-form
        ref="passwordFormRef"
        :model="passwordForm"
        :rules="passwordRules"
        label-width="80px"
      >
        <el-form-item label="原密码" prop="oldPassword">
          <el-input 
            v-model="passwordForm.oldPassword" 
            type="password"
            placeholder="请输入原密码"
            show-password
          />
        </el-form-item>
        
        <el-form-item label="新密码" prop="newPassword">
          <el-input 
            v-model="passwordForm.newPassword" 
            type="password"
            placeholder="请输入新密码（至少6位）"
            show-password
          />
        </el-form-item>
        
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input 
            v-model="passwordForm.confirmPassword" 
            type="password"
            placeholder="请再次输入新密码"
            show-password
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handlePasswordClose">取消</el-button>
          <el-button 
            type="primary" 
            @click="handlePasswordSubmit"
            :loading="passwordLoading"
          >
            修改密码
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, onMounted, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  ArrowLeft, 
  User, 
  Lock, 
  ShoppingBag, 
  ShoppingCart, 
  House 
} from '@element-plus/icons-vue'
import { userAPI } from '@/api/user'

export default {
  name: 'UserProfile',
  components: {
    ArrowLeft,
    User,
    Lock,
    ShoppingBag,
    ShoppingCart,
    House
  },
  setup() {
    const router = useRouter()
    
    // 响应式数据
    const loading = ref(false)
    const userInfo = ref({})
    const showEditDialog = ref(false)
    const showPasswordDialog = ref(false)
    const editLoading = ref(false)
    const passwordLoading = ref(false)
    
    // 编辑表单
    const editForm = reactive({
      username: '',
      email: '',
      phone: ''
    })
    
    // 密码表单
    const passwordForm = reactive({
      oldPassword: '',
      newPassword: '',
      confirmPassword: ''
    })
    
    // 表单验证规则
    const editRules = {
      email: [
        { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
      ],
      phone: [
        { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
      ]
    }
    
    const passwordRules = {
      oldPassword: [
        { required: true, message: '请输入原密码', trigger: 'blur' }
      ],
      newPassword: [
        { required: true, message: '请输入新密码', trigger: 'blur' },
        { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
      ],
      confirmPassword: [
        { required: true, message: '请确认新密码', trigger: 'blur' },
        {
          validator: (rule, value, callback) => {
            if (value !== passwordForm.newPassword) {
              callback(new Error('两次输入的密码不一致'))
            } else {
              callback()
            }
          },
          trigger: 'blur'
        }
      ]
    }
    
    // 获取当前用户ID（从localStorage获取）
    const getCurrentUserId = () => {
      try {
        const userInfo = localStorage.getItem('userInfo')
        if (userInfo) {
          const userData = JSON.parse(userInfo)
          return userData.id
        }
        return null
      } catch (error) {
        console.error('获取用户ID失败:', error)
        return null
      }
    }
    
    // 加载用户信息
    const loadUserInfo = async () => {
      loading.value = true
      try {
        const userId = getCurrentUserId()

        if (!userId) {
          ElMessage.error('用户未登录，请先登录')
          router.push('/user-login')
          return
        }

        const response = await userAPI.getUserProfile(userId)
        
        if (response.success) {
          userInfo.value = response.data
          // 初始化编辑表单
          editForm.username = response.data.username
          editForm.email = response.data.email || ''
          editForm.phone = response.data.phone || ''
        } else {
          ElMessage.error(response.message || '获取用户信息失败')
        }
      } catch (error) {
        console.error('获取用户信息失败:', error)
        ElMessage.error('获取用户信息失败')
      } finally {
        loading.value = false
      }
    }
    
    // 返回商城主页
    const goBack = () => {
      router.push('/mall')
    }
    
    // 退出登录
    const logout = async () => {
      try {
        await ElMessageBox.confirm('确定要退出登录吗？', '退出登录', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        
        // 清除用户登录状态
        localStorage.removeItem('userInfo')
        localStorage.removeItem('isLoggedIn')
        
        ElMessage.success('已退出登录')
        router.push('/user-login')
      } catch (error) {
        // 用户取消退出
      }
    }
    
    // 跳转到订单页面
    const goToOrders = () => {
      router.push('/orders')
    }
    
    // 跳转到购物车
    const goToCart = () => {
      router.push('/cart')
    }
    
    // 跳转到商城
    const goToMall = () => {
      router.push('/mall')
    }
    
    // 处理编辑对话框关闭
    const handleEditClose = () => {
      showEditDialog.value = false
      // 重置表单
      editForm.username = userInfo.value.username
      editForm.email = userInfo.value.email || ''
      editForm.phone = userInfo.value.phone || ''
    }
    
    // 提交编辑信息
    const handleEditSubmit = async () => {
      editLoading.value = true
      try {
        const userId = getCurrentUserId()

        if (!userId) {
          ElMessage.error('用户未登录，请先登录')
          router.push('/user-login')
          return
        }

        const updateData = {
          email: editForm.email,
          phone: editForm.phone
        }
        
        const response = await userAPI.updateUserProfile(userId, updateData)
        
        if (response.success) {
          ElMessage.success('信息更新成功')
          userInfo.value = response.data
          showEditDialog.value = false
        } else {
          ElMessage.error(response.message || '更新失败')
        }
      } catch (error) {
        console.error('更新用户信息失败:', error)
        ElMessage.error('更新失败')
      } finally {
        editLoading.value = false
      }
    }
    
    // 处理密码对话框关闭
    const handlePasswordClose = () => {
      showPasswordDialog.value = false
      // 重置表单
      passwordForm.oldPassword = ''
      passwordForm.newPassword = ''
      passwordForm.confirmPassword = ''
    }
    
    // 提交修改密码
    const handlePasswordSubmit = async () => {
      passwordLoading.value = true
      try {
        const userId = getCurrentUserId()

        if (!userId) {
          ElMessage.error('用户未登录，请先登录')
          router.push('/user-login')
          return
        }

        const passwordData = {
          oldPassword: passwordForm.oldPassword,
          newPassword: passwordForm.newPassword
        }
        
        const response = await userAPI.changePassword(userId, passwordData)
        
        if (response.success) {
          ElMessage.success('密码修改成功')
          showPasswordDialog.value = false
          handlePasswordClose()
        } else {
          ElMessage.error(response.message || '密码修改失败')
        }
      } catch (error) {
        console.error('修改密码失败:', error)
        ElMessage.error('修改密码失败')
      } finally {
        passwordLoading.value = false
      }
    }
    
    // 格式化时间
    const formatTime = (timeStr) => {
      if (!timeStr) return ''
      const date = new Date(timeStr)
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      })
    }
    
    // 组件挂载时加载数据
    onMounted(() => {
      loadUserInfo()
    })
    
    return {
      loading,
      userInfo,
      showEditDialog,
      showPasswordDialog,
      editLoading,
      passwordLoading,
      editForm,
      passwordForm,
      editRules,
      passwordRules,
      goBack,
      logout,
      goToOrders,
      goToCart,
      goToMall,
      handleEditClose,
      handleEditSubmit,
      handlePasswordClose,
      handlePasswordSubmit,
      formatTime,
      ArrowLeft,
      User,
      Lock,
      ShoppingBag,
      ShoppingCart,
      House
    }
  }
}
</script>

<style scoped>
.profile-container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* 顶部导航样式 */
.profile-header {
  background: linear-gradient(135deg, #ff6a00 0%, #ff8533 50%, #ffb366 100%);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 1000;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 70px;
}

.back-btn, .logout-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
}

.back-btn:hover, .logout-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
}

.page-title {
  color: white;
  font-size: 24px;
  font-weight: 600;
  margin: 0;
}

/* 主要内容样式 */
.profile-main {
  padding: 20px;
}

.profile-content {
  max-width: 1200px;
  margin: 0 auto;
}

.loading-container {
  background: white;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.profile-sections {
  display: grid;
  gap: 20px;
  grid-template-columns: 1fr;
}

/* 卡片通用样式 */
.info-card,
.security-card,
.actions-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  border: 1px solid #f0f0f0;
  transition: all 0.3s ease;
}

.info-card:hover,
.security-card:hover,
.actions-card:hover {
  box-shadow: 0 6px 24px rgba(255, 106, 0, 0.15);
  transform: translateY(-2px);
}

.card-header {
  padding: 20px 30px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.card-header h2 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #ff6a00;
}

.card-header .el-button--primary {
  background: linear-gradient(135deg, #ff6a00, #ff8533);
  border: none;
  border-radius: 20px;
}

.card-header .el-button--primary:hover {
  background: linear-gradient(135deg, #ff5500, #ff7722);
}

/* 修改密码按钮样式 - 与快捷操作按钮保持一致 */
.password-btn {
  min-width: 100px;
  height: 40px;
  font-size: 14px;
  border-radius: 20px;
  font-weight: 600;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: linear-gradient(135deg, #ff6a00, #ff8533);
  border: none;
  color: white;
  box-shadow: 0 2px 8px rgba(255, 106, 0, 0.2);
  position: relative;
  overflow: hidden;
}

.password-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.password-btn:hover::before {
  left: 100%;
}

.password-btn:hover {
  background: linear-gradient(135deg, #ff5500, #ff7722);
  transform: translateY(-3px);
  box-shadow: 0 6px 16px rgba(255, 106, 0, 0.4);
}

/* 用户信息卡片样式 */
.user-info {
  padding: 30px;
}

.avatar-section {
  display: flex;
  align-items: center;
  margin-bottom: 30px;
}

.user-avatar {
  margin-right: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.user-basic h3 {
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: 600;
  color: #333;
}

.user-status {
  margin: 0;
}

.info-details {
  display: grid;
  gap: 16px;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.info-row {
  display: flex;
  align-items: center;
}

.info-row .label {
  width: 100px;
  color: #666;
  font-size: 14px;
}

.info-row .value {
  color: #333;
  font-size: 14px;
  font-weight: 500;
}

/* 安全卡片样式 */
.security-items {
  padding: 30px;
}

.security-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 0;
  border-bottom: 1px solid #f0f0f0;
}

.security-item:last-child {
  border-bottom: none;
}

.item-info {
  display: flex;
  align-items: center;
}

.item-icon {
  font-size: 24px;
  color: #667eea;
  margin-right: 15px;
}

.item-details h4 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.item-details p {
  margin: 0;
  font-size: 14px;
  color: #666;
}

/* 操作卡片样式 */
.action-buttons {
  padding: 30px;
  display: flex;
  gap: 15px;
  justify-content: center;
  flex-wrap: wrap;
}

.action-btn {
  min-width: 120px;
  height: 50px;
  font-size: 16px;
  border-radius: 25px;
  font-weight: 600;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.action-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.action-btn:hover::before {
  left: 100%;
}

/* 我的订单按钮 - 深橙色主题 */
.action-btn.el-button--primary {
  background: linear-gradient(135deg, #ff6a00, #ff8533);
  border: none;
  color: white;
  box-shadow: 0 2px 8px rgba(255, 106, 0, 0.2);
}

.action-btn.el-button--primary:hover {
  background: linear-gradient(135deg, #ff5500, #ff7722);
  transform: translateY(-3px);
  box-shadow: 0 6px 16px rgba(255, 106, 0, 0.4);
}

/* 购物车按钮 - 蓝色主题 */
.action-btn.el-button--success {
  background: linear-gradient(135deg, #409eff, #66b3ff);
  border: none;
  color: white;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
}

.action-btn.el-button--success:hover {
  background: linear-gradient(135deg, #337ecc, #5599dd);
  transform: translateY(-3px);
  box-shadow: 0 6px 16px rgba(64, 158, 255, 0.4);
}

/* 返回商城按钮 - 浅橙色主题 */
.action-btn.el-button--info {
  background: linear-gradient(135deg, #ff8533, #ffb366);
  border: none;
  color: white;
  box-shadow: 0 2px 8px rgba(255, 133, 51, 0.2);
}

.action-btn.el-button--info:hover {
  background: linear-gradient(135deg, #ff7722, #ffa555);
  transform: translateY(-3px);
  box-shadow: 0 6px 16px rgba(255, 133, 51, 0.4);
}

/* 对话框样式 */
.form-tip {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .profile-main {
    padding: 10px;
  }

  .header-content {
    padding: 0 15px;
  }

  .page-title {
    font-size: 20px;
  }

  .card-header,
  .user-info,
  .security-items,
  .action-buttons {
    padding: 20px;
  }

  .avatar-section {
    flex-direction: column;
    text-align: center;
    gap: 15px;
  }

  .user-avatar {
    margin-right: 0;
  }

  .info-details {
    grid-template-columns: 1fr;
  }

  .security-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .action-buttons {
    flex-direction: column;
    align-items: center;
  }

  .action-btn {
    width: 100%;
    max-width: 300px;
  }
}

/* 桌面端大屏幕优化 */
@media (min-width: 1200px) {
  .profile-sections {
    grid-template-columns: 2fr 1fr;
    grid-template-areas:
      "info security"
      "actions actions";
  }

  .info-card {
    grid-area: info;
  }

  .security-card {
    grid-area: security;
  }

  .actions-card {
    grid-area: actions;
  }
}
</style>
