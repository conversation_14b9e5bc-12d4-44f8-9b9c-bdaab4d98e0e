<template>
  <div class="admin-register-container">
    <!-- 背景装饰 -->
    <div class="background-decoration">
      <div class="floating-shape shape-1"></div>
      <div class="floating-shape shape-2"></div>
      <div class="floating-shape shape-3"></div>
      <div class="floating-shape shape-4"></div>
    </div>

    <!-- 主要内容 -->
    <div class="main-content">
      <div class="content-wrapper">
        <!-- 返回按钮和注册卡片的容器 -->
        <div class="card-with-button-container">
          <!-- 返回按钮 -->
          <div class="back-button">
            <button
              @click="goBack"
              class="back-btn"
            >
              <el-icon class="back-icon">
                <ArrowLeft />
              </el-icon>
              <span class="back-text">返回管理后台</span>
            </button>
          </div>

          <!-- 注册卡片 -->
          <div class="register-card">
            <div class="card-border"></div>
            <div class="card-content">
              <div class="register-header">
                <div class="icon-wrapper">
                  <div class="icon-bg">
                    <el-icon class="register-icon">
                      <UserFilled />
                    </el-icon>
                  </div>
                </div>
                <h1 class="register-title">管理员注册</h1>
                <p class="register-subtitle">创建新的管理员账户</p>
              </div>
            
              <!-- 注册表单 -->
              <el-form
                ref="registerFormRef"
                :model="registerForm"
                :rules="registerRules"
                class="register-form"
                size="large"
              >
                <el-form-item prop="username">
                  <div class="input-wrapper">
                    <el-input
                      v-model="registerForm.username"
                      placeholder="请输入管理员用户名"
                      :prefix-icon="User"
                      clearable
                      class="custom-input"
                    />
                  </div>
                </el-form-item>

                <el-form-item prop="email">
                  <div class="input-wrapper">
                    <el-input
                      v-model="registerForm.email"
                      placeholder="请输入邮箱"
                      :prefix-icon="Message"
                      clearable
                      class="custom-input"
                    />
                  </div>
                </el-form-item>

                <el-form-item prop="phone">
                  <div class="input-wrapper">
                    <el-input
                      v-model="registerForm.phone"
                      placeholder="请输入手机号"
                      :prefix-icon="Phone"
                      clearable
                      class="custom-input"
                    />
                  </div>
                </el-form-item>

                <el-form-item prop="password">
                  <div class="input-wrapper">
                    <el-input
                      v-model="registerForm.password"
                      type="password"
                      placeholder="请输入密码"
                      :prefix-icon="Lock"
                      show-password
                      clearable
                      class="custom-input"
                    />
                  </div>
                </el-form-item>

                <el-form-item prop="confirmPassword">
                  <div class="input-wrapper">
                    <el-input
                      v-model="registerForm.confirmPassword"
                      type="password"
                      placeholder="请确认密码"
                      :prefix-icon="Lock"
                      show-password
                      clearable
                      class="custom-input"
                    />
                  </div>
                </el-form-item>

                <el-form-item>
                  <button
                    type="button"
                    class="register-button"
                    :disabled="loading"
                    @click="handleRegister"
                  >
                    <span class="button-content">
                      <el-icon v-if="!loading" class="button-icon"><UserFilled /></el-icon>
                      <span class="button-text">{{ loading ? '注册中...' : '创建管理员' }}</span>
                    </span>
                    <div class="button-glow"></div>
                  </button>
                </el-form-item>
              </el-form>

              <!-- 提示信息 -->
              <div class="admin-notice">
                <div class="notice-content">
                  <el-icon class="notice-icon"><Warning /></el-icon>
                  <div class="notice-text">
                    <div class="notice-title">管理员权限说明</div>
                    <div class="notice-description">新创建的管理员将拥有系统管理权限，请谨慎操作。</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  ArrowLeft,
  UserFilled,
  User,
  Message,
  Phone,
  Lock,
  Warning
} from '@element-plus/icons-vue'
import adminAPI from '@/api/admin.js'

export default {
  name: 'AdminRegister',
  components: {
    ArrowLeft,
    UserFilled,
    User,
    Message,
    Phone,
    Lock,
    Warning
  },
  setup() {
    const router = useRouter()
    const registerFormRef = ref(null)
    const loading = ref(false)

    // 注册表单数据
    const registerForm = reactive({
      username: '',
      email: '',
      phone: '',
      password: '',
      confirmPassword: ''
    })

    // 表单验证规则
    const registerRules = {
      username: [
        { required: true, message: '请输入管理员用户名', trigger: 'blur' },
        { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' },
        { pattern: /^[a-zA-Z0-9_]+$/, message: '用户名只能包含字母、数字和下划线', trigger: 'blur' }
      ],
      email: [
        { required: true, message: '请输入邮箱地址', trigger: 'blur' },
        { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
      ],
      phone: [
        { required: true, message: '请输入手机号', trigger: 'blur' },
        { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
      ],
      password: [
        { required: true, message: '请输入密码', trigger: 'blur' },
        { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
      ],
      confirmPassword: [
        { required: true, message: '请确认密码', trigger: 'blur' },
        {
          validator: (rule, value, callback) => {
            if (value !== registerForm.password) {
              callback(new Error('两次输入的密码不一致'))
            } else {
              callback()
            }
          },
          trigger: 'blur'
        }
      ]
    }

    // 返回管理后台
    const goBack = () => {
      router.push('/admin/dashboard')
    }

    // 处理注册
    const handleRegister = async () => {
      if (!registerFormRef.value) return

      try {
        const valid = await registerFormRef.value.validate()
        if (valid) {
          loading.value = true

          try {
            const response = await adminAPI.addAdmin({
              username: registerForm.username,
              email: registerForm.email,
              phone: registerForm.phone,
              password: registerForm.password,
              status: 2 // 管理员状态
            })

            if (response.success) {
              ElMessage.success('管理员创建成功！')
              // 清空表单
              resetForm()
              // 延迟跳转回管理后台
              setTimeout(() => {
                router.push('/admin/dashboard')
              }, 1500)
            } else {
              ElMessage.error(response.message || '管理员创建失败')
            }
          } catch (error) {
            console.error('管理员注册失败:', error)
            if (error && error.message) {
              ElMessage.error(error.message)
            } else {
              ElMessage.error('管理员创建失败，请重试')
            }
          }
        }
      } catch (error) {
        console.error('表单验证失败:', error)
      } finally {
        loading.value = false
      }
    }

    // 重置表单
    const resetForm = () => {
      registerForm.username = ''
      registerForm.email = ''
      registerForm.phone = ''
      registerForm.password = ''
      registerForm.confirmPassword = ''
      if (registerFormRef.value) {
        registerFormRef.value.clearValidate()
      }
    }

    return {
      registerFormRef,
      loading,
      registerForm,
      registerRules,
      goBack,
      handleRegister,
      resetForm
    }
  }
}
</script>

<style scoped>
/* 基础容器样式 */
.admin-register-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* 背景装饰 */
.background-decoration {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.floating-shape {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.shape-1 {
  width: 80px;
  height: 80px;
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.shape-2 {
  width: 120px;
  height: 120px;
  top: 60%;
  right: 15%;
  animation-delay: 2s;
}

.shape-3 {
  width: 60px;
  height: 60px;
  bottom: 30%;
  left: 20%;
  animation-delay: 4s;
}

.shape-4 {
  width: 100px;
  height: 100px;
  top: 10%;
  right: 30%;
  animation-delay: 1s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.7;
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
    opacity: 1;
  }
}

/* 主要内容 */
.main-content {
  position: relative;
  z-index: 2;
  width: 100%;
  max-width: 1200px;
  padding: 20px;
}

.content-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 80vh;
}

/* 卡片和按钮容器 */
.card-with-button-container {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* 返回按钮 */
.back-button {
  position: absolute;
  top: 5px;
  left: 0;
  z-index: 10;
}

.back-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: rgba(255, 255, 255, 0.15);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 25px;
  color: white;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.back-btn:hover {
  background: rgba(255, 255, 255, 0.25);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.back-icon {
  font-size: 16px;
}

.back-text {
  font-size: 14px;
}

/* 注册卡片 */
.register-card {
  width: 100%;
  max-width: 450px;
  margin-top: 55px;
  position: relative;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 3px solid #ff6a00;
  overflow: hidden;
}

.card-border {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 20px;
  padding: 3px;
  background: linear-gradient(135deg, #ff6a00, #ff8533, #ff6a00);
  z-index: -1;
}

.card-content {
  background: white;
  border-radius: 17px;
  padding: 40px 35px;
  position: relative;
  z-index: 1;
}

/* 注册头部 */
.register-header {
  text-align: center;
  margin-bottom: 35px;
}

.icon-wrapper {
  margin-bottom: 20px;
}

.icon-bg {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #ff6a00 0%, #ff8533 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  box-shadow: 0 10px 30px rgba(255, 106, 0, 0.3);
}

.register-icon {
  font-size: 35px;
  color: white;
}

.register-title {
  font-size: 28px;
  font-weight: 700;
  color: #2c3e50;
  margin: 0 0 8px 0;
  letter-spacing: -0.5px;
}

.register-subtitle {
  font-size: 16px;
  color: #7f8c8d;
  margin: 0;
  font-weight: 400;
}

/* 表单样式 */
.register-form {
  margin-bottom: 25px;
}

.input-wrapper {
  margin-bottom: 20px;
}

.custom-input {
  height: 50px;
  border-radius: 12px;
  border: 2px solid #e1e8ed;
  transition: all 0.3s ease;
}

.custom-input:hover {
  border-color: #ff8533;
}

.custom-input.is-focus {
  border-color: #ff6a00;
  box-shadow: 0 0 0 3px rgba(255, 106, 0, 0.1);
}

.custom-input .el-input__wrapper {
  border: none;
  box-shadow: none;
  background: transparent;
  padding: 0 15px;
}

.custom-input .el-input__inner {
  font-size: 15px;
  color: #2c3e50;
  font-weight: 500;
}

.custom-input .el-input__inner::placeholder {
  color: #95a5a6;
  font-weight: 400;
}

/* 注册按钮 */
.register-button {
  width: 100%;
  height: 55px;
  background: linear-gradient(135deg, #ff6a00 0%, #ff8533 100%);
  border: none;
  border-radius: 12px;
  color: white;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  margin-bottom: 20px;
}

.register-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(255, 106, 0, 0.3);
}

.register-button:active {
  transform: translateY(0);
}

.register-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.button-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  position: relative;
  z-index: 2;
}

.button-icon {
  font-size: 18px;
}

.button-text {
  font-size: 16px;
}

.button-glow {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s ease;
}

.register-button:hover .button-glow {
  left: 100%;
}

/* 管理员提示 */
.admin-notice {
  background: #fff3e0;
  border: 1px solid #ffcc80;
  border-radius: 12px;
  padding: 16px;
  margin-top: 20px;
}

.notice-content {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.notice-icon {
  color: #ff9800;
  font-size: 20px;
  margin-top: 2px;
}

.notice-text {
  flex: 1;
}

.notice-title {
  font-size: 14px;
  font-weight: 600;
  color: #e65100;
  margin-bottom: 4px;
}

.notice-description {
  font-size: 13px;
  color: #bf360c;
  line-height: 1.4;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .admin-register-container {
    padding: 10px;
  }

  .register-card {
    max-width: 100%;
    margin-top: 70px;
  }

  .card-content {
    padding: 30px 25px;
  }

  .register-title {
    font-size: 24px;
  }

  .register-subtitle {
    font-size: 14px;
  }

  .back-button {
    position: relative;
    top: auto;
    left: auto;
    margin-bottom: 20px;
    align-self: flex-start;
  }

  .card-with-button-container {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .card-content {
    padding: 25px 20px;
  }

  .icon-bg {
    width: 60px;
    height: 60px;
  }

  .register-icon {
    font-size: 28px;
  }

  .register-title {
    font-size: 22px;
  }

  .custom-input {
    height: 45px;
  }

  .register-button {
    height: 50px;
  }
}
</style>
