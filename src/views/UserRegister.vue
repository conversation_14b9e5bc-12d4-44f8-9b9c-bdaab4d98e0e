<template>
  <div class="user-register-container">
    <!-- 背景装饰 -->
    <div class="background-decoration">
      <div class="floating-shape shape-1"></div>
      <div class="floating-shape shape-2"></div>
      <div class="floating-shape shape-3"></div>
      <div class="floating-shape shape-4"></div>
    </div>

    <!-- 主要内容 -->
    <div class="main-content">
      <div class="content-wrapper">
        <!-- 返回按钮和注册卡片的容器 -->
        <div class="card-with-button-container">
          <!-- 返回按钮 -->
          <div class="back-button">
            <button
              @click="goBack"
              class="back-btn"
            >
              <el-icon class="back-icon">
                <ArrowLeft />
              </el-icon>
              <span class="back-text">返回登录</span>
            </button>
          </div>

          <!-- 注册卡片 -->
          <div class="register-card">
        <div class="card-border"></div>
        <div class="card-content">
          <div class="register-header">
            <div class="icon-wrapper">
              <div class="icon-bg">
                <el-icon class="register-icon">
                  <UserFilled />
                </el-icon>
              </div>
            </div>
            <h1 class="register-title">用户注册</h1>
            <p class="register-subtitle">创建您的清风商城账户</p>
          </div>
        
          <!-- 注册表单 -->
          <el-form
            ref="registerFormRef"
            :model="registerForm"
            :rules="registerRules"
            class="register-form"
            size="large"
          >
            <el-form-item prop="username">
              <div class="input-wrapper">
                <el-input
                  v-model="registerForm.username"
                  placeholder="请输入用户名"
                  :prefix-icon="User"
                  clearable
                  class="custom-input"
                />
              </div>
            </el-form-item>

            <el-form-item prop="email">
              <div class="input-wrapper">
                <el-input
                  v-model="registerForm.email"
                  placeholder="请输入邮箱"
                  :prefix-icon="Message"
                  clearable
                  class="custom-input"
                />
              </div>
            </el-form-item>

            <el-form-item prop="phone">
              <div class="input-wrapper">
                <el-input
                  v-model="registerForm.phone"
                  placeholder="请输入手机号"
                  :prefix-icon="Phone"
                  clearable
                  class="custom-input"
                />
              </div>
            </el-form-item>

            <el-form-item prop="password">
              <div class="input-wrapper">
                <el-input
                  v-model="registerForm.password"
                  type="password"
                  placeholder="请输入密码"
                  :prefix-icon="Lock"
                  show-password
                  clearable
                  class="custom-input"
                />
              </div>
            </el-form-item>

            <el-form-item prop="confirmPassword">
              <div class="input-wrapper">
                <el-input
                  v-model="registerForm.confirmPassword"
                  type="password"
                  placeholder="请确认密码"
                  :prefix-icon="Lock"
                  show-password
                  clearable
                  class="custom-input"
                />
              </div>
            </el-form-item>

            <el-form-item prop="agreement">
              <div class="agreement-wrapper">
                <el-checkbox v-model="registerForm.agreement" class="agreement-checkbox">
                  我已阅读并同意
                  <el-link :underline="false" class="agreement-link">《用户协议》</el-link>
                  和
                  <el-link :underline="false" class="agreement-link">《隐私政策》</el-link>
                </el-checkbox>
              </div>
            </el-form-item>

            <el-form-item>
              <button
                type="button"
                class="register-button"
                :disabled="loading"
                @click="handleRegister"
              >
                <span class="button-content">
                  <el-icon v-if="!loading" class="button-icon"><UserFilled /></el-icon>
                  <span class="button-text">{{ loading ? '注册中...' : '立即注册' }}</span>
                </span>
                <div class="button-glow"></div>
              </button>
            </el-form-item>
          </el-form>

          <!-- 登录链接 -->
          <div class="login-link">
            <span>已有账户？</span>
            <el-link
              :underline="false"
              @click="goToLogin"
              class="login-btn"
            >
              立即登录
            </el-link>
          </div>
        </div>
        </div>
      </div>
    </div>
  </div>
</div>
</template>

<script>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { ArrowLeft, User, UserFilled, Lock, Message, Phone } from '@element-plus/icons-vue'
import { userAPI } from '@/api/user'

export default {
  name: 'UserRegister',
  components: {
    UserFilled
  },
  setup() {
    const router = useRouter()
    const registerFormRef = ref()
    const loading = ref(false)
    
    // 注册表单数据
    const registerForm = reactive({
      username: '',
      email: '',
      phone: '',
      password: '',
      confirmPassword: '',
      agreement: false
    })
    
    // 自定义验证函数
    const validateConfirmPassword = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请再次输入密码'))
      } else if (value !== registerForm.password) {
        callback(new Error('两次输入密码不一致'))
      } else {
        callback()
      }
    }
    
    const validateAgreement = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请阅读并同意用户协议'))
      } else {
        callback()
      }
    }
    
    // 表单验证规则
    const registerRules = {
      username: [
        { required: true, message: '请输入用户名', trigger: 'blur' },
        { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' },
        { pattern: /^[a-zA-Z0-9_]+$/, message: '用户名只能包含字母、数字和下划线', trigger: 'blur' }
      ],
      email: [
        { required: true, message: '请输入邮箱地址', trigger: 'blur' },
        { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
      ],
      phone: [
        { required: true, message: '请输入手机号', trigger: 'blur' },
        { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
      ],
      password: [
        { required: true, message: '请输入密码', trigger: 'blur' },
        { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' },
        { pattern: /^(?=.*[a-zA-Z])(?=.*\d)/, message: '密码必须包含字母和数字', trigger: 'blur' }
      ],
      confirmPassword: [
        { required: true, validator: validateConfirmPassword, trigger: 'blur' }
      ],
      agreement: [
        { required: true, validator: validateAgreement, trigger: 'change' }
      ]
    }
    
    // 返回上一页
    const goBack = () => {
      router.go(-1)
    }
    
    // 跳转到登录页面
    const goToLogin = () => {
      router.push('/user/login')
    }
    
    // 处理注册
    const handleRegister = async () => {
      if (!registerFormRef.value) return

      try {
        const valid = await registerFormRef.value.validate()
        if (valid) {
          loading.value = true

          try {
            // 调用注册API
            const response = await userAPI.register({
              username: registerForm.username,
              email: registerForm.email,
              phone: registerForm.phone,
              password: registerForm.password
            })

            if (response.success) {
              ElMessage.success(response.message || '注册成功！请登录您的账户')

              // 注册成功后跳转到登录页面
              router.push('/user/login')
            } else {
              ElMessage.error(response.message || '注册失败')
            }
          } catch (error) {
            console.error('注册请求失败:', error)
            ElMessage.error(error.message || '注册失败，请检查网络连接')
          } finally {
            loading.value = false
          }
        }
      } catch (error) {
        console.log('表单验证失败:', error)
      }
    }
    
    return {
      registerFormRef,
      registerForm,
      registerRules,
      loading,
      goBack,
      goToLogin,
      handleRegister,
      ArrowLeft,
      User,
      UserFilled,
      Lock,
      Message,
      Phone
    }
  }
}
</script>

<style scoped>
/* 主容器 */
.user-register-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #ff6a00 0%, #ff8533 50%, #ffb366 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  padding: 20px;
  font-family: 'PingFang SC', 'Microsoft YaHei', 'Helvetica Neue', Arial, sans-serif;
}

/* 背景装饰 */
.background-decoration {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.floating-shape {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 8s ease-in-out infinite;
}

.shape-1 {
  width: 120px;
  height: 120px;
  top: 15%;
  left: 10%;
  animation-delay: 0s;
  background: linear-gradient(45deg, rgba(255, 255, 255, 0.15), rgba(255, 200, 100, 0.2));
}

.shape-2 {
  width: 80px;
  height: 80px;
  top: 70%;
  right: 15%;
  animation-delay: 2s;
  background: linear-gradient(45deg, rgba(255, 255, 255, 0.1), rgba(255, 179, 102, 0.15));
}

.shape-3 {
  width: 60px;
  height: 60px;
  bottom: 20%;
  left: 20%;
  animation-delay: 4s;
  background: linear-gradient(45deg, rgba(255, 255, 255, 0.12), rgba(255, 133, 51, 0.18));
}

.shape-4 {
  width: 100px;
  height: 100px;
  top: 30%;
  right: 25%;
  animation-delay: 6s;
  background: linear-gradient(45deg, rgba(255, 255, 255, 0.08), rgba(255, 200, 100, 0.12));
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg) scale(1);
    opacity: 0.6;
  }
  50% {
    transform: translateY(-30px) rotate(180deg) scale(1.1);
    opacity: 0.3;
  }
}

/* 主要内容 */
.main-content {
  position: relative;
  z-index: 1;
  width: 100%;
  max-width: 600px;
}

/* 内容包装器 */
.content-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  width: 100%;
}

/* 卡片和按钮容器 */
.card-with-button-container {
  display: flex;
  align-items: flex-start;
  gap: 20px;
  width: 100%;
  max-width: 600px;
  justify-content: center;
}

/* 返回按钮 */
.back-button {
  flex-shrink: 0;
  margin-top: 0;
}

.back-btn {
  background: rgba(255, 255, 255, 0.25);
  border: 2px solid rgba(255, 255, 255, 0.4);
  color: white;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
  border-radius: 25px;
  padding: 12px 20px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  outline: none;
}

.back-btn:hover {
  background: rgba(255, 255, 255, 0.35);
  border-color: rgba(255, 255, 255, 0.6);
  transform: translateX(-4px) scale(1.05);
  box-shadow: 0 8px 25px rgba(255, 255, 255, 0.2);
}

.back-icon {
  font-size: 16px;
  transition: transform 0.3s ease;
}

.back-btn:hover .back-icon {
  transform: translateX(-2px);
}

.back-text {
  white-space: nowrap;
  font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
}

/* 注册卡片 */
.register-card {
  border-radius: 24px;
  overflow: hidden;
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.98), rgba(255, 255, 255, 0.92));
  backdrop-filter: blur(20px);
  border: 3px solid transparent;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
  position: relative;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  width: 100%;
  max-width: 450px;
  flex: 1;
}

.card-border {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 24px;
  padding: 3px;
  background: linear-gradient(135deg, #ff6a00, #ff8533, #ffb366, #ff6a00);
  mask: linear-gradient(to bottom, #fff 0%, #fff 100%) content-box, linear-gradient(to bottom, #fff 0%, #fff 100%);
  mask-composite: xor;
  -webkit-mask: linear-gradient(to bottom, #fff 0%, #fff 100%) content-box, linear-gradient(to bottom, #fff 0%, #fff 100%);
  -webkit-mask-composite: xor;
}

.card-content {
  padding: 40px;
  position: relative;
  z-index: 1;
}

/* 注册头部 */
.register-header {
  text-align: center;
  margin-bottom: 35px;
}

.icon-wrapper {
  margin-bottom: 25px;
  display: flex;
  justify-content: center;
}

.icon-bg {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg, #ff6a00, #ff8533);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8px 25px rgba(255, 106, 0, 0.3);
  position: relative;
  overflow: hidden;
}

.icon-bg::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
  50% { transform: translateX(100%) translateY(100%) rotate(45deg); }
  100% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
}

.register-icon {
  font-size: 2.5rem;
  color: white;
  z-index: 1;
}

.register-title {
  font-size: 2.2rem;
  font-weight: 700;
  background: linear-gradient(135deg, #ff6a00, #ff8533);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-bottom: 12px;
  letter-spacing: -0.5px;
}

.register-subtitle {
  color: #666;
  font-size: 1rem;
  margin: 0;
  font-weight: 400;
}

/* 注册表单 */
.register-form {
  margin-top: 35px;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.register-form :deep(.el-form-item) {
  margin-bottom: 20px;
  width: 100%;
  max-width: 400px;
  display: flex;
  justify-content: center;
}

.input-wrapper {
  position: relative;
  width: 100%;
  max-width: 400px;
  margin: 0 auto;
}

.custom-input {
  width: 100%;
}

.custom-input :deep(.el-input__wrapper) {
  border-radius: 16px;
  border: 2px solid #e8e8e8;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  padding: 12px 16px;
  height: 52px;
  width: 100%;
}

.custom-input :deep(.el-input__wrapper:hover) {
  border-color: #ff8533;
  box-shadow: 0 6px 20px rgba(255, 133, 51, 0.15);
}

.custom-input :deep(.el-input__wrapper.is-focus) {
  border-color: #ff6a00;
  box-shadow: 0 0 0 3px rgba(255, 106, 0, 0.15);
}

.custom-input :deep(.el-input__inner) {
  font-size: 15px;
  color: #333;
  font-weight: 500;
}

.custom-input :deep(.el-input__prefix) {
  color: #ff6a00;
}

/* 协议复选框 */
.agreement-wrapper {
  width: 100%;
  max-width: 400px;
  margin: 0 auto;
  text-align: center;
}

.agreement-checkbox :deep(.el-checkbox__label) {
  color: #666;
  font-weight: 500;
  font-size: 14px;
}

.agreement-checkbox :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
  background-color: #ff6a00;
  border-color: #ff6a00;
}

.agreement-link {
  color: #ff6a00 !important;
  font-weight: 600;
  transition: all 0.3s ease;
}

.agreement-link:hover {
  color: #ff8533 !important;
}

/* 注册按钮 */
.register-button {
  width: 100%;
  max-width: 400px;
  height: 56px;
  border: none;
  border-radius: 28px;
  background: linear-gradient(135deg, #ff6a00, #ff8533);
  color: white;
  font-size: 1.1rem;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  box-shadow: 0 8px 25px rgba(255, 106, 0, 0.3);
  margin: 15px auto 0;
}

.register-button:hover:not(:disabled) {
  transform: translateY(-3px);
  box-shadow: 0 12px 35px rgba(255, 106, 0, 0.4);
  background: linear-gradient(135deg, #ff5500, #ff6a00);
}

.register-button:active:not(:disabled) {
  transform: translateY(-1px);
}

.register-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.button-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  position: relative;
  z-index: 1;
}

.button-icon {
  font-size: 1.2rem;
}

.button-text {
  font-weight: 700;
}

.button-glow {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.6s ease;
}

.register-button:hover .button-glow {
  left: 100%;
}

/* 登录链接 */
.login-link {
  text-align: center;
  margin: 25px auto 0;
  color: #666;
  font-size: 15px;
  width: 100%;
  max-width: 400px;
}

.login-btn {
  margin-left: 8px;
  font-weight: 700;
  color: #ff6a00 !important;
  transition: all 0.3s ease;
}

.login-btn:hover {
  color: #ff8533 !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-content {
    max-width: 500px;
  }

  .content-wrapper {
    flex-direction: column;
    align-items: center;
    gap: 15px;
  }

  .card-with-button-container {
    flex-direction: column;
    align-items: center;
    gap: 20px;
  }

  .back-button {
    align-self: flex-start;
  }
}

@media (max-width: 480px) {
  .main-content {
    max-width: 380px;
  }

  .card-content {
    padding: 35px 25px;
  }

  .register-title {
    font-size: 1.8rem;
  }

  .card-with-button-container {
    flex-direction: column;
    align-items: center;
    gap: 15px;
  }

  .back-btn {
    padding: 10px 16px;
    font-size: 13px;
  }

  .back-icon {
    font-size: 14px;
  }

  .icon-bg {
    width: 70px;
    height: 70px;
  }

  .register-icon {
    font-size: 2.2rem;
  }

  .input-wrapper {
    max-width: 340px;
  }

  .register-form :deep(.el-form-item) {
    max-width: 340px;
  }

  .custom-input :deep(.el-input__wrapper) {
    height: 48px;
    padding: 10px 14px;
  }

  .agreement-wrapper {
    max-width: 340px;
  }

  .register-button {
    max-width: 340px;
    height: 52px;
    font-size: 1rem;
  }

  .login-link {
    max-width: 340px;
  }
}

@media (max-width: 360px) {
  .card-content {
    padding: 30px 20px;
  }

  .register-title {
    font-size: 1.6rem;
  }

  .input-wrapper {
    max-width: 300px;
  }

  .register-form :deep(.el-form-item) {
    max-width: 300px;
  }

  .agreement-wrapper {
    max-width: 300px;
  }

  .register-button {
    max-width: 300px;
  }

  .login-link {
    max-width: 300px;
  }
}
</style>
