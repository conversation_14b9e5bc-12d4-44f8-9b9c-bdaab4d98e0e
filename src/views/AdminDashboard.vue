<template>
  <div class="admin-dashboard">
    <!-- 顶部导航栏 -->
    <header class="admin-header">
      <div class="header-content">
        <div class="logo-section">
          <el-icon class="logo-icon"><Management /></el-icon>
          <h1 class="system-title">商城后台管理系统</h1>
        </div>
        
        <div class="header-actions">
          <div class="admin-info">
            <el-avatar :size="40" class="admin-avatar">
              <el-icon><UserFilled /></el-icon>
            </el-avatar>
            <span class="admin-name">{{ adminInfo.username }}</span>
          </div>
          
          <el-dropdown @command="handleCommand" class="admin-dropdown">
            <el-button type="primary" class="dropdown-btn">
              <el-icon><Setting /></el-icon>
              <el-icon class="el-icon--right"><ArrowDown /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="profile">个人设置</el-dropdown-item>
                <el-dropdown-item command="logout" divided>退出登录</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
    </header>

    <!-- 主要内容区域 -->
    <div class="admin-main">
      <!-- 左侧菜单 -->
      <aside class="admin-sidebar">
        <el-menu
          :default-active="activeMenu"
          class="admin-menu"
          @select="handleMenuSelect"
          background-color="#304156"
          text-color="#bfcbd9"
          active-text-color="#409EFF"
        >
          <el-menu-item index="dashboard" class="menu-item">
            <el-icon><House /></el-icon>
            <span>首页</span>
          </el-menu-item>
          
          <el-menu-item index="categories" class="menu-item">
            <el-icon><Grid /></el-icon>
            <span>分类管理</span>
          </el-menu-item>
          
          <el-menu-item index="products" class="menu-item">
            <el-icon><Goods /></el-icon>
            <span>商品管理</span>
          </el-menu-item>
          
          <el-menu-item index="orders" class="menu-item">
            <el-icon><Document /></el-icon>
            <span>订单管理</span>
          </el-menu-item>
        </el-menu>
      </aside>

      <!-- 右侧内容区域 -->
      <main class="admin-content">
        <div class="content-header">
          <div class="breadcrumb-section">
            <el-breadcrumb separator="/">
              <el-breadcrumb-item>管理后台</el-breadcrumb-item>
              <el-breadcrumb-item>{{ getCurrentPageTitle() }}</el-breadcrumb-item>
            </el-breadcrumb>
          </div>
        </div>
        
        <div class="content-body">
          <!-- 首页内容 -->
          <div v-if="activeMenu === 'dashboard'" class="dashboard-content">
            <div class="stats-cards">
              <div class="stat-card">
                <div class="stat-icon products-icon">
                  <el-icon><Goods /></el-icon>
                </div>
                <div class="stat-info">
                  <h3>商品总数</h3>
                  <p class="stat-number">{{ stats.totalProducts }}</p>
                </div>
              </div>
              
              <div class="stat-card">
                <div class="stat-icon categories-icon">
                  <el-icon><Grid /></el-icon>
                </div>
                <div class="stat-info">
                  <h3>分类总数</h3>
                  <p class="stat-number">{{ stats.totalCategories }}</p>
                </div>
              </div>
              
              <div class="stat-card">
                <div class="stat-icon orders-icon">
                  <el-icon><Document /></el-icon>
                </div>
                <div class="stat-info">
                  <h3>订单总数</h3>
                  <p class="stat-number">{{ stats.totalOrders }}</p>
                </div>
              </div>
              
              <div class="stat-card">
                <div class="stat-icon users-icon">
                  <el-icon><User /></el-icon>
                </div>
                <div class="stat-info">
                  <h3>用户总数</h3>
                  <p class="stat-number">{{ stats.totalUsers }}</p>
                </div>
              </div>
            </div>
            
            <div class="dashboard-charts">
              <div class="chart-card">
                <div class="chart-header">
                  <h3>系统概览</h3>
                  <el-button
                    type="primary"
                    size="small"
                    :icon="Refresh"
                    @click="loadStats"
                    :loading="statsLoading"
                  >
                    刷新数据
                  </el-button>
                </div>
                <p>欢迎使用商品管理系统！</p>
                <p>请从左侧菜单选择要管理的功能模块。</p>
                <p class="stats-update-time" v-if="statsUpdateTime">
                  数据更新时间：{{ statsUpdateTime }}
                </p>
              </div>
            </div>
          </div>
          
          <!-- 分类管理内容 -->
          <div v-else-if="activeMenu === 'categories'" class="categories-content">
            <CategoryManagement />
          </div>
          
          <!-- 商品管理内容 -->
          <div v-else-if="activeMenu === 'products'" class="products-content">
            <ProductManagement />
          </div>
          
          <!-- 订单管理内容 -->
          <div v-else-if="activeMenu === 'orders'" class="orders-content">
            <OrderManagement />
          </div>
        </div>
      </main>
    </div>

    <!-- 个人设置对话框 -->
    <el-dialog
      v-model="profileDialogVisible"
      title="个人设置"
      width="600px"
      :before-close="handleProfileDialogClose"
    >
      <div class="profile-content">
        <!-- 管理员信息展示 -->
        <div class="admin-info-section">
          <div class="section-title">
            <el-icon><User /></el-icon>
            <span>管理员信息</span>
          </div>
          <div class="info-card">
            <div class="avatar-section">
              <el-avatar :size="80" class="profile-avatar">
                <el-icon><UserFilled /></el-icon>
              </el-avatar>
            </div>
            <div class="info-details">
              <div class="info-item">
                <label>用户名：</label>
                <span>{{ adminInfo.username }}</span>
              </div>
              <div class="info-item">
                <label>邮箱：</label>
                <span>{{ adminInfo.email || '未设置' }}</span>
              </div>
              <div class="info-item">
                <label>角色：</label>
                <span>系统管理员</span>
              </div>
              <div class="info-item">
                <label>状态：</label>
                <el-tag type="success">正常</el-tag>
              </div>
              <div class="info-item">
                <label>最后登录：</label>
                <span>{{ formatLoginTime() }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 功能按钮区域 -->
        <div class="action-section">
          <div class="section-title">
            <el-icon><Setting /></el-icon>
            <span>管理功能</span>
          </div>
          <div class="action-buttons">
            <el-button
              type="primary"
              size="large"
              :icon="Lock"
              @click="showChangePasswordDialog"
              class="action-btn"
            >
              修改密码
            </el-button>
            <el-button
              type="success"
              size="large"
              :icon="Plus"
              @click="showAddAdminDialog"
              class="action-btn"
            >
              添加新管理员
            </el-button>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 修改密码对话框 -->
    <el-dialog
      v-model="changePasswordDialogVisible"
      title="修改密码"
      width="450px"
      :before-close="handlePasswordDialogClose"
    >
      <el-form
        ref="passwordFormRef"
        :model="passwordForm"
        :rules="passwordRules"
        label-width="100px"
        class="password-form"
      >
        <el-form-item label="当前密码" prop="oldPassword">
          <el-input
            v-model="passwordForm.oldPassword"
            type="password"
            placeholder="请输入当前密码"
            show-password
          />
        </el-form-item>
        <el-form-item label="新密码" prop="newPassword">
          <el-input
            v-model="passwordForm.newPassword"
            type="password"
            placeholder="请输入新密码"
            show-password
          />
        </el-form-item>
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input
            v-model="passwordForm.confirmPassword"
            type="password"
            placeholder="请再次输入新密码"
            show-password
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handlePasswordDialogClose">取消</el-button>
          <el-button
            type="primary"
            @click="handleChangePassword"
            :loading="passwordSubmitting"
          >
            确认修改
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Management,
  UserFilled,
  Setting,
  ArrowDown,
  House,
  Grid,
  Goods,
  Document,
  User,
  Plus,
  Refresh,
  Lock
} from '@element-plus/icons-vue'
import CategoryManagement from '@/components/CategoryManagement.vue'
import ProductManagement from '@/components/ProductManagement.vue'
import OrderManagement from '@/components/OrderManagement.vue'
import ordersAPI from '@/api/orders.js'
import adminAPI from '@/api/admin.js'

export default {
  name: 'AdminDashboard',
  components: {
    Management,
    UserFilled,
    Setting,
    ArrowDown,
    House,
    Grid,
    Goods,
    Document,
    User,
    Plus,
    Refresh,
    Lock,
    CategoryManagement,
    ProductManagement,
    OrderManagement
  },
  setup() {
    const router = useRouter()
    
    // 响应式数据
    const activeMenu = ref('dashboard')
    const adminInfo = reactive({
      username: '管理员',
      avatar: ''
    })
    
    const stats = reactive({
      totalProducts: 0,
      totalCategories: 0,
      totalOrders: 0,
      totalUsers: 0
    })

    const statsLoading = ref(false)
    const statsUpdateTime = ref('')

    // 个人设置相关数据
    const profileDialogVisible = ref(false)
    const changePasswordDialogVisible = ref(false)
    const passwordSubmitting = ref(false)
    const passwordFormRef = ref(null)

    // 修改密码表单数据
    const passwordForm = reactive({
      oldPassword: '',
      newPassword: '',
      confirmPassword: ''
    })

    // 密码表单验证规则
    const passwordRules = {
      oldPassword: [
        { required: true, message: '请输入当前密码', trigger: 'blur' }
      ],
      newPassword: [
        { required: true, message: '请输入新密码', trigger: 'blur' },
        { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
      ],
      confirmPassword: [
        { required: true, message: '请确认新密码', trigger: 'blur' },
        {
          validator: (rule, value, callback) => {
            if (value !== passwordForm.newPassword) {
              callback(new Error('两次输入的密码不一致'))
            } else {
              callback()
            }
          },
          trigger: 'blur'
        }
      ]
    }
    
    // 获取当前页面标题
    const getCurrentPageTitle = () => {
      const titles = {
        dashboard: '首页',
        categories: '分类管理',
        products: '商品管理',
        orders: '订单管理'
      }
      return titles[activeMenu.value] || '首页'
    }
    
    // 处理菜单选择
    const handleMenuSelect = (index) => {
      activeMenu.value = index
      console.log('选择菜单:', index)
    }
    
    // 处理顶部下拉菜单命令
    const handleCommand = (command) => {
      switch (command) {
        case 'profile':
          profileDialogVisible.value = true
          break
        case 'logout':
          handleLogout()
          break
      }
    }
    
    // 退出登录
    const handleLogout = async () => {
      try {
        await ElMessageBox.confirm('确定要退出登录吗？', '退出登录', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        
        // 清除管理员登录状态
        localStorage.removeItem('adminInfo')
        localStorage.removeItem('isAdminLoggedIn')
        
        ElMessage.success('已退出登录')
        router.push('/admin/login')
      } catch (error) {
        // 用户取消退出
      }
    }
    



    
    // 加载管理员信息
    const loadAdminInfo = () => {
      try {
        const adminData = localStorage.getItem('adminInfo')
        if (adminData) {
          const admin = JSON.parse(adminData)
          adminInfo.username = admin.username || '管理员'
        }
      } catch (error) {
        console.error('加载管理员信息失败:', error)
      }
    }
    
    // 加载统计数据
    const loadStats = async () => {
      statsLoading.value = true
      try {
        console.log('开始加载管理员首页统计数据...')
        const response = await ordersAPI.getAdminDashboardStatistics()

        console.log('统计数据API响应:', response)

        if (response.success) {
          const data = response.data
          stats.totalProducts = data.totalProducts || 0
          stats.totalCategories = data.totalCategories || 0
          stats.totalOrders = data.totalOrders || 0
          stats.totalUsers = data.totalUsers || 0

          // 更新时间
          statsUpdateTime.value = new Date().toLocaleString('zh-CN')

          console.log('统计数据加载成功:', stats)
          ElMessage.success('统计数据已更新')
        } else {
          console.error('获取统计数据失败:', response.message)
          ElMessage.error(response.message || '获取统计数据失败')

          // 失败时使用默认值
          stats.totalProducts = 0
          stats.totalCategories = 0
          stats.totalOrders = 0
          stats.totalUsers = 0
        }
      } catch (error) {
        console.error('加载统计数据失败:', error)
        ElMessage.error('加载统计数据失败：' + error.message)

        // 异常时使用默认值
        stats.totalProducts = 0
        stats.totalCategories = 0
        stats.totalOrders = 0
        stats.totalUsers = 0
      } finally {
        statsLoading.value = false
      }
    }

    // 格式化登录时间
    const formatLoginTime = () => {
      const now = new Date()
      return now.toLocaleString('zh-CN')
    }

    // 显示修改密码对话框
    const showChangePasswordDialog = () => {
      changePasswordDialogVisible.value = true
      profileDialogVisible.value = false
    }

    // 显示添加管理员对话框（跳转到管理员注册页面）
    const showAddAdminDialog = () => {
      profileDialogVisible.value = false
      router.push('/admin/register')
    }

    // 关闭个人设置对话框
    const handleProfileDialogClose = () => {
      profileDialogVisible.value = false
    }

    // 关闭修改密码对话框
    const handlePasswordDialogClose = () => {
      changePasswordDialogVisible.value = false
      resetPasswordForm()
    }

    // 重置密码表单
    const resetPasswordForm = () => {
      passwordForm.oldPassword = ''
      passwordForm.newPassword = ''
      passwordForm.confirmPassword = ''
      if (passwordFormRef.value) {
        passwordFormRef.value.clearValidate()
      }
    }

    // 处理修改密码
    const handleChangePassword = async () => {
      if (!passwordFormRef.value) return

      try {
        const valid = await passwordFormRef.value.validate()
        if (valid) {
          passwordSubmitting.value = true

          try {
            // 调用修改密码API
            const response = await adminAPI.changePassword({
              username: adminInfo.username,
              oldPassword: passwordForm.oldPassword,
              newPassword: passwordForm.newPassword
            })

            console.log('修改密码API响应:', response)

            if (response.success) {
              ElMessage.success('密码修改成功！')
              handlePasswordDialogClose()
            } else {
              ElMessage.error(response.message || '密码修改失败')
            }
          } catch (apiError) {
            console.error('修改密码API调用失败:', apiError)
            if (apiError && apiError.message) {
              ElMessage.error(apiError.message || '密码修改失败')
            } else {
              ElMessage.error('密码修改失败，请检查网络连接')
            }
          }
        }
      } catch (error) {
        console.error('表单验证失败:', error)
      } finally {
        passwordSubmitting.value = false
      }
    }

    // 组件挂载时执行
    onMounted(() => {
      loadAdminInfo()
      loadStats()
    })
    
    return {
      activeMenu,
      adminInfo,
      stats,
      statsLoading,
      statsUpdateTime,
      profileDialogVisible,
      changePasswordDialogVisible,
      passwordSubmitting,
      passwordFormRef,
      passwordForm,
      passwordRules,
      getCurrentPageTitle,
      handleMenuSelect,
      handleCommand,
      handleLogout,
      loadStats,
      formatLoginTime,
      showChangePasswordDialog,
      showAddAdminDialog,
      handleProfileDialogClose,
      handlePasswordDialogClose,
      resetPasswordForm,
      handleChangePassword
    }
  }
}
</script>

<style scoped>
.admin-dashboard {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f0f2f5;
}

/* 顶部导航栏样式 */
.admin-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 1000;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
  height: 64px;
}

.logo-section {
  display: flex;
  align-items: center;
  color: white;
}

.logo-icon {
  font-size: 32px;
  margin-right: 12px;
}

.system-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.admin-info {
  display: flex;
  align-items: center;
  gap: 8px;
  color: white;
}

.admin-avatar {
  background: rgba(255, 255, 255, 0.2);
}

.admin-name {
  font-size: 14px;
  font-weight: 500;
}

.dropdown-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
}

.dropdown-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
}

/* 主要内容区域样式 */
.admin-main {
  display: flex;
  flex: 1;
  overflow: hidden;
}

/* 左侧菜单样式 */
.admin-sidebar {
  width: 240px;
  background-color: #304156;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
}

.admin-menu {
  border: none;
  height: 100%;
}

.menu-item {
  height: 56px;
  line-height: 56px;
  margin: 0 8px;
  border-radius: 8px;
  transition: all 0.3s;
}

.menu-item:hover {
  background-color: rgba(64, 158, 255, 0.1);
}

.menu-item.is-active {
  background-color: #409EFF;
  color: white;
}

.menu-item .el-icon {
  margin-right: 8px;
  font-size: 18px;
}

/* 右侧内容区域样式 */
.admin-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.content-header {
  background: white;
  padding: 16px 24px;
  border-bottom: 1px solid #e8eaec;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
}

.breadcrumb-section .el-breadcrumb {
  font-size: 14px;
}

.content-body {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
}

/* 首页统计卡片样式 */
.stats-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  display: flex;
  align-items: center;
  transition: transform 0.3s, box-shadow 0.3s;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
  color: white;
}

.products-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.categories-icon {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.orders-icon {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.users-icon {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-info h3 {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.stat-number {
  margin: 0;
  font-size: 28px;
  font-weight: 700;
  color: #333;
}

/* 图表卡片样式 */
.dashboard-charts {
  display: grid;
  gap: 20px;
}

.chart-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.chart-card h3 {
  margin: 0;
  font-size: 18px;
  color: #333;
  font-weight: 600;
}

.chart-card p {
  margin: 8px 0;
  color: #666;
  line-height: 1.6;
}

.stats-update-time {
  font-size: 12px;
  color: #999;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

/* 页面头部样式 */
.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24px;
  padding: 20px 24px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.page-header h2 {
  margin: 0;
  font-size: 20px;
  color: #333;
  font-weight: 600;
}

/* 内容占位符样式 */
.content-placeholder {
  background: white;
  border-radius: 12px;
  padding: 60px 24px;
  text-align: center;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.content-placeholder p {
  margin: 0;
  font-size: 16px;
  color: #999;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .admin-main {
    flex-direction: column;
  }

  .admin-sidebar {
    width: 100%;
    height: auto;
  }

  .admin-menu {
    display: flex;
    overflow-x: auto;
  }

  .menu-item {
    min-width: 120px;
    margin: 0 4px;
  }

  .stats-cards {
    grid-template-columns: 1fr;
  }

  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }

  .header-content {
    padding: 0 16px;
  }

  .content-body {
    padding: 16px;
  }
}

@media (max-width: 480px) {
  .logo-section {
    flex-direction: column;
    align-items: flex-start;
  }

  .system-title {
    font-size: 16px;
  }

  .admin-info {
    flex-direction: column;
    gap: 4px;
  }

  .stat-card {
    flex-direction: column;
    text-align: center;
  }

  .stat-icon {
    margin-right: 0;
    margin-bottom: 12px;
  }
}

/* 个人设置对话框样式 */
.profile-content {
  padding: 20px 0;
}

.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.section-title .el-icon {
  margin-right: 8px;
  color: #409EFF;
}

/* 管理员信息卡片 */
.admin-info-section {
  margin-bottom: 32px;
}

.info-card {
  display: flex;
  padding: 24px;
  background: #f8f9fa;
  border-radius: 12px;
  border: 1px solid #e4e7ed;
}

.avatar-section {
  margin-right: 24px;
}

.profile-avatar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-size: 32px;
}

.info-details {
  flex: 1;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  font-size: 14px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-item label {
  width: 80px;
  color: #606266;
  font-weight: 500;
}

.info-item span {
  color: #303133;
}

/* 功能按钮区域 */
.action-section {
  border-top: 1px solid #e4e7ed;
  padding-top: 24px;
}

.action-buttons {
  display: flex;
  gap: 16px;
}

.action-btn {
  flex: 1;
  height: 48px;
  font-size: 16px;
  border-radius: 8px;
  transition: all 0.3s;
}

.action-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 修改密码表单样式 */
.password-form {
  padding: 20px 0;
}

.password-form .el-form-item {
  margin-bottom: 20px;
}

.dialog-footer {
  text-align: right;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .info-card {
    flex-direction: column;
    text-align: center;
  }

  .avatar-section {
    margin-right: 0;
    margin-bottom: 20px;
  }

  .action-buttons {
    flex-direction: column;
  }

  .action-btn {
    width: 100%;
  }
}
</style>
